#!/usr/bin/env python3
"""
Drywall Optimization Program
Main application entry point
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys

from dxf_handler import DXFHandler
from canvas_viewer import CanvasViewer
from material_manager import MaterialManager
from geometry_utils import GeometryUtils


class MainApplication:
    def __init__(self, root):
        self.root = root
        self.root.title("Drywall Optimization Program")
        self.root.geometry("1400x800")
        self.root.minsize(800, 600)
        
        # Initialize components
        self.dxf_handler = DXFHandler()
        self.geometry_utils = GeometryUtils()
        self.selected_shape = None
        self.materials = []
        
        self.setup_ui()
        self.setup_bindings()
        
    def setup_ui(self):
        """Setup the main user interface"""
        # Main container
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Menu bar
        self.create_menu()
        
        # Toolbar
        self.create_toolbar()
        
        # Main content area with resizable panes
        self.create_main_content()
        
    def create_menu(self):
        """Create menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Fájl", menu=file_menu)
        file_menu.add_command(label="DXF betöltése", command=self.load_dxf)
        file_menu.add_separator()
        file_menu.add_command(label="Kilépés", command=self.root.quit)
        
        # Materials menu
        materials_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Anyagok", menu=materials_menu)
        materials_menu.add_command(label="Anyag hozzáadása", command=self.add_material)
        materials_menu.add_command(label="Anyagok kezelése", command=self.manage_materials)
        
    def create_toolbar(self):
        """Create toolbar with main actions"""
        toolbar = ttk.Frame(self.main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 5))
        
        # Load DXF button
        ttk.Button(toolbar, text="DXF betöltése", 
                  command=self.load_dxf).pack(side=tk.LEFT, padx=(0, 5))
        
        # Select shape button
        self.select_shape_btn = ttk.Button(toolbar, text="Falforma kiválasztása", 
                                          command=self.toggle_shape_selection,
                                          state=tk.DISABLED)
        self.select_shape_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Process button
        self.process_btn = ttk.Button(toolbar, text="Feldolgozás (Enter)", 
                                     command=self.process_materials,
                                     state=tk.DISABLED)
        self.process_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Status label
        self.status_label = ttk.Label(toolbar, text="Kész a használatra")
        self.status_label.pack(side=tk.RIGHT)
        
    def create_main_content(self):
        """Create main content area with resizable panes"""
        # Create PanedWindow for resizable panels
        self.paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True)
        
        # Left panel - DXF Canvas
        self.left_frame = ttk.LabelFrame(self.paned_window, text="DXF Viewer - Sandbox")
        self.paned_window.add(self.left_frame, weight=3)
        
        # Right panel - Materials
        self.right_frame = ttk.LabelFrame(self.paned_window, text="Eszköztár - Anyagok")
        self.paned_window.add(self.right_frame, weight=1)
        
        # Initialize canvas viewer
        self.canvas_viewer = CanvasViewer(self.left_frame, self)
        
        # Initialize material manager
        self.material_manager = MaterialManager(self.right_frame, self)

        # Initially disable materials until shape is selected
        self.material_manager.disable_materials()
        
    def setup_bindings(self):
        """Setup keyboard and event bindings"""
        self.root.bind('<Return>', lambda e: self.process_materials())
        self.root.bind('<Control-o>', lambda e: self.load_dxf())
        
    def load_dxf(self):
        """Load DXF file"""
        file_path = filedialog.askopenfilename(
            title="DXF fájl kiválasztása",
            filetypes=[("DXF files", "*.dxf"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                self.status_label.config(text="DXF betöltése...")
                self.root.update()
                
                # Load DXF file
                entities, closed_shapes = self.dxf_handler.load_dxf(file_path)
                
                # Display in canvas
                self.canvas_viewer.load_entities(entities, closed_shapes)
                
                # Enable shape selection
                self.select_shape_btn.config(state=tk.NORMAL)
                
                self.status_label.config(text=f"DXF betöltve: {len(closed_shapes)} zárt alakzat")
                
            except Exception as e:
                messagebox.showerror("Hiba", f"DXF betöltési hiba: {str(e)}")
                self.status_label.config(text="Hiba a DXF betöltésekor")
                
    def toggle_shape_selection(self):
        """Toggle shape selection mode"""
        self.canvas_viewer.toggle_selection_mode()
        
    def on_shape_selected(self, shape):
        """Called when a shape is selected"""
        self.selected_shape = shape
        self.material_manager.enable_materials()
        self.process_btn.config(state=tk.NORMAL)
        self.status_label.config(text="Falforma kiválasztva - húzza be az anyagokat")
        
    def add_material(self):
        """Add new material type"""
        self.material_manager.add_material_dialog()
        
    def manage_materials(self):
        """Open materials management dialog"""
        self.material_manager.manage_materials_dialog()
        
    def process_materials(self):
        """Process materials on canvas (Enter key functionality)"""
        if not self.selected_shape:
            messagebox.showwarning("Figyelem", "Először válasszon ki egy falformát!")
            return
            
        try:
            self.status_label.config(text="Feldolgozás...")
            self.root.update()
            
            # Get materials on canvas
            canvas_materials = self.canvas_viewer.get_canvas_materials()
            
            if not canvas_materials:
                messagebox.showinfo("Info", "Nincs anyag a canvas-on!")
                return
                
            # Process intersections and cuts
            results = self.geometry_utils.process_intersections(
                self.selected_shape, canvas_materials
            )
            
            # Update canvas and materials
            self.canvas_viewer.update_after_processing(results)
            self.material_manager.update_after_processing(results)
            
            self.status_label.config(text="Feldolgozás kész")
            
        except Exception as e:
            messagebox.showerror("Hiba", f"Feldolgozási hiba: {str(e)}")
            self.status_label.config(text="Hiba a feldolgozáskor")


def main():
    """Main entry point"""
    root = tk.Tk()
    app = MainApplication(root)
    root.mainloop()


if __name__ == "__main__":
    main()
