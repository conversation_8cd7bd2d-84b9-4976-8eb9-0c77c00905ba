#!/usr/bin/env python3
"""
Test complex shape handling - L-shaped, U-shaped materials and wall forms
"""

from geometry_utils import GeometryUtils
from shapely.geometry import Polygon
import json

def test_l_shaped_wall_with_rectangular_material():
    """Test L-shaped wall form with rectangular material"""
    print("Testing L-shaped wall with rectangular material...")
    
    geom_utils = GeometryUtils()
    
    # Create L-shaped wall form
    l_wall = Polygon([
        (0, 0), (100, 0), (100, 40), (40, 40), (40, 100), (0, 100)
    ])
    
    wall_shape = {
        'geometry': l_wall,
        'type': 'L-shaped wall',
        'bounds': l_wall.bounds
    }
    
    # Create rectangular material that overlaps
    rect_material = {
        'name': 'Rectangular Material',
        'width': 80,
        'height': 80,
        'color': '#90EE90',
        'x': 50,  # Positioned to overlap both arms of L
        'y': 50
    }
    
    # Process intersection
    results = geom_utils.process_intersections(wall_shape, [rect_material])
    
    print(f"✓ L-wall area: {l_wall.area}")
    print(f"✓ Coverage: {results['coverage']:.1f}%")
    print(f"✓ Materials to keep: {len(results['materials_to_keep'])}")
    print(f"✓ Remainders: {len(results['remainders'])}")
    
    # Check that we have a complex remainder
    if results['remainders']:
        remainder = results['remainders'][0]
        print(f"  Remainder: {remainder['name']}")
        print(f"  Complex shape: {remainder.get('is_complex_shape', False)}")
        print(f"  Area: {remainder.get('area', 0):.1f}")
        
        if 'geometry' in remainder:
            geom = remainder['geometry']
            print(f"  Geometry type: {geom.geom_type}")
            print(f"  Geometry valid: {geom.is_valid}")
    
    return len(results['remainders']) > 0

def test_u_shaped_material_with_rectangular_wall():
    """Test U-shaped material with rectangular wall"""
    print("\nTesting U-shaped material with rectangular wall...")
    
    geom_utils = GeometryUtils()
    
    # Create simple rectangular wall
    rect_wall = Polygon([
        (20, 20), (80, 20), (80, 80), (20, 80)
    ])
    
    wall_shape = {
        'geometry': rect_wall,
        'type': 'rectangular wall',
        'bounds': rect_wall.bounds
    }
    
    # Create U-shaped material (simulate a remainder from previous cutting)
    u_shape = Polygon([
        (0, 0), (100, 0), (100, 20), (80, 20), (80, 60), (20, 60), (20, 20), (0, 20)
    ])
    
    u_material = {
        'name': 'U-shaped Remainder',
        'width': 100,  # Bounding box
        'height': 60,
        'color': '#FFB6C1',
        'x': 50,
        'y': 40,
        'geometry': u_shape,
        'is_complex_shape': True,
        'area': u_shape.area
    }
    
    # Process intersection
    results = geom_utils.process_intersections(wall_shape, [u_material])
    
    print(f"✓ U-material area: {u_shape.area}")
    print(f"✓ Wall area: {rect_wall.area}")
    print(f"✓ Coverage: {results['coverage']:.1f}%")
    print(f"✓ Materials to keep: {len(results['materials_to_keep'])}")
    print(f"✓ Remainders: {len(results['remainders'])}")
    
    # Check intersected material
    if results['materials_to_keep']:
        intersected = results['materials_to_keep'][0]
        print(f"  Intersected: {intersected['name']}")
        print(f"  Complex shape: {intersected.get('is_complex_shape', False)}")
        if 'geometry' in intersected:
            print(f"  Intersection area: {intersected['geometry'].area:.1f}")
    
    # Check remainders
    if results['remainders']:
        remainder = results['remainders'][0]
        print(f"  Remainder: {remainder['name']}")
        print(f"  Complex shape: {remainder.get('is_complex_shape', False)}")
        if 'geometry' in remainder:
            print(f"  Remainder area: {remainder['geometry'].area:.1f}")
    
    return len(results['materials_to_keep']) > 0

def test_complex_wall_with_complex_material():
    """Test complex wall with complex material"""
    print("\nTesting complex wall with complex material...")
    
    geom_utils = GeometryUtils()
    
    # Create T-shaped wall
    t_wall = Polygon([
        (20, 0), (80, 0), (80, 20), (100, 20), (100, 60), (0, 60), (0, 20), (20, 20)
    ])
    
    wall_shape = {
        'geometry': t_wall,
        'type': 'T-shaped wall',
        'bounds': t_wall.bounds
    }
    
    # Create L-shaped material
    l_material_geom = Polygon([
        (10, 10), (70, 10), (70, 30), (30, 30), (30, 70), (10, 70)
    ])
    
    l_material = {
        'name': 'L-shaped Material',
        'width': 60,
        'height': 60,
        'color': '#FFFF00',
        'x': 40,
        'y': 40,
        'geometry': l_material_geom,
        'is_complex_shape': True,
        'area': l_material_geom.area
    }
    
    # Process intersection
    results = geom_utils.process_intersections(wall_shape, [l_material])
    
    print(f"✓ T-wall area: {t_wall.area}")
    print(f"✓ L-material area: {l_material_geom.area}")
    print(f"✓ Coverage: {results['coverage']:.1f}%")
    print(f"✓ Materials to keep: {len(results['materials_to_keep'])}")
    print(f"✓ Remainders: {len(results['remainders'])}")
    
    # Detailed analysis
    if results['materials_to_keep']:
        intersected = results['materials_to_keep'][0]
        if 'geometry' in intersected:
            intersection_area = intersected['geometry'].area
            print(f"  Intersection area: {intersection_area:.1f}")
            print(f"  Intersection type: {intersected['geometry'].geom_type}")
    
    if results['remainders']:
        for i, remainder in enumerate(results['remainders']):
            print(f"  Remainder {i+1}: {remainder['name']}")
            if 'geometry' in remainder:
                print(f"    Area: {remainder['geometry'].area:.1f}")
                print(f"    Type: {remainder['geometry'].geom_type}")
    
    return len(results['materials_to_keep']) > 0 and len(results['remainders']) > 0

def test_material_positioning():
    """Test that complex materials are positioned correctly"""
    print("\nTesting material positioning...")
    
    geom_utils = GeometryUtils()
    
    # Create a simple L-shaped geometry at origin
    l_shape = Polygon([
        (0, 0), (30, 0), (30, 10), (10, 10), (10, 30), (0, 30)
    ])
    
    # Create material with this geometry positioned at (50, 50)
    material = {
        'name': 'Positioned L-shape',
        'width': 30,
        'height': 30,
        'color': '#FF0000',
        'x': 50,
        'y': 50,
        'geometry': l_shape,
        'is_complex_shape': True
    }
    
    # Get the positioned geometry
    positioned_geom = geom_utils.create_material_geometry(material)
    
    # Check that it's positioned correctly
    centroid = positioned_geom.centroid
    expected_x, expected_y = 50, 50
    
    print(f"✓ Original centroid: ({l_shape.centroid.x:.1f}, {l_shape.centroid.y:.1f})")
    print(f"✓ Positioned centroid: ({centroid.x:.1f}, {centroid.y:.1f})")
    print(f"✓ Expected position: ({expected_x}, {expected_y})")
    
    # Check if positioning is approximately correct (within tolerance)
    tolerance = 5.0
    x_ok = abs(centroid.x - expected_x) < tolerance
    y_ok = abs(centroid.y - expected_y) < tolerance
    
    print(f"✓ X position correct: {x_ok}")
    print(f"✓ Y position correct: {y_ok}")
    
    return x_ok and y_ok

def test_serialization_of_complex_materials():
    """Test that complex materials can be saved/loaded"""
    print("\nTesting serialization of complex materials...")
    
    # Create complex materials
    materials = [
        {
            'name': 'Simple Rectangle',
            'width': 100,
            'height': 50,
            'color': '#90EE90',
            'quantity': 3
        },
        {
            'name': 'L-shaped Remainder',
            'width': 60,
            'height': 60,
            'color': '#FFB6C1',
            'quantity': 1,
            'is_complex_shape': True,
            'area': 2700.0
            # Note: geometry cannot be serialized directly to JSON
        }
    ]
    
    try:
        # Test JSON serialization
        json_str = json.dumps(materials, ensure_ascii=False, indent=2)
        loaded_materials = json.loads(json_str)
        
        print(f"✓ Serialized {len(materials)} materials")
        print(f"✓ Loaded {len(loaded_materials)} materials")
        
        # Check that complex shape flag is preserved
        complex_materials = [m for m in loaded_materials if m.get('is_complex_shape', False)]
        print(f"✓ Complex materials preserved: {len(complex_materials)}")
        
        return len(complex_materials) > 0
        
    except Exception as e:
        print(f"✗ Serialization failed: {e}")
        return False

def main():
    """Run all complex shape tests"""
    print("Complex Shape Handling Test Suite")
    print("=" * 60)
    
    tests = [
        ("L-shaped Wall + Rectangular Material", test_l_shaped_wall_with_rectangular_material),
        ("U-shaped Material + Rectangular Wall", test_u_shaped_material_with_rectangular_wall),
        ("Complex Wall + Complex Material", test_complex_wall_with_complex_material),
        ("Material Positioning", test_material_positioning),
        ("Serialization", test_serialization_of_complex_materials)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✓ {test_name} PASSED")
            else:
                print(f"\n✗ {test_name} FAILED")
        except Exception as e:
            print(f"\n✗ {test_name} FAILED: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"Complex Shape Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Complex shape handling works perfectly!")
        print("\nSupported features:")
        print("- L-shaped, U-shaped, T-shaped wall forms")
        print("- Complex remainder materials")
        print("- Exact geometric intersections")
        print("- Proper positioning of complex shapes")
        print("- Serialization support (without geometry)")
    else:
        print("⚠️  Some complex shape features need attention.")
        
    return passed == total

if __name__ == "__main__":
    main()
