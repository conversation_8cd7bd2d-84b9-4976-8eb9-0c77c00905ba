"""
Geometry utilities for intersection calculations and material processing
"""

import numpy as np
from shapely.geometry import <PERSON>y<PERSON>, Point, box
from shapely.ops import unary_union
import math


class GeometryUtils:
    def __init__(self):
        self.tolerance = 1e-6
        
    def process_intersections(self, selected_shape, canvas_materials):
        """Process intersections between selected shape and materials on canvas"""
        if not selected_shape or not canvas_materials:
            return {'used_materials': [], 'remainders': [], 'coverage': 0.0, 'materials_to_keep': [], 'materials_to_remove': []}

        results = {
            'used_materials': [],
            'remainders': [],
            'coverage': 0.0,
            'total_area': 0.0,
            'covered_area': 0.0,
            'materials_to_keep': [],  # Materials that stay on canvas (intersect with shape)
            'materials_to_remove': []  # Materials to remove from canvas
        }

        # Get the target shape geometry
        target_geometry = selected_shape['geometry']
        target_area = target_geometry.area
        results['total_area'] = target_area

        covered_area = 0.0

        for material in canvas_materials:
            # Create material geometry
            material_geometry = self.create_material_geometry(material)

            # Calculate intersection
            intersection = target_geometry.intersection(material_geometry)

            if intersection.area > self.tolerance:
                # Material intersects with target shape

                # The part that intersects stays on canvas (but cut to shape)
                intersected_material = material.copy()
                intersected_material['geometry'] = intersection
                # Update position to intersection center
                bounds = intersection.bounds
                intersected_material['x'] = (bounds[0] + bounds[2]) / 2
                intersected_material['y'] = (bounds[1] + bounds[3]) / 2
                intersected_material['width'] = bounds[2] - bounds[0]
                intersected_material['height'] = bounds[3] - bounds[1]

                results['materials_to_keep'].append(intersected_material)
                covered_area += intersection.area

                # Calculate remainder (material - intersection)
                remainder = material_geometry.difference(intersection)

                if remainder.area > self.tolerance:
                    # Create remainder pieces to add back to material manager
                    remainder_pieces = self.split_remainder(remainder, material)
                    results['remainders'].extend(remainder_pieces)

                # Mark original material for removal from canvas
                results['materials_to_remove'].append(material)

            else:
                # Material doesn't intersect - remove it from canvas and add back to materials
                self.add_material_back_to_inventory(material, results)
                results['materials_to_remove'].append(material)

        results['covered_area'] = covered_area
        results['coverage'] = (covered_area / target_area) * 100 if target_area > 0 else 0

        return results

    def add_material_back_to_inventory(self, material, results):
        """Add a material back to inventory as a remainder piece"""
        remainder_piece = {
            'name': f"Visszaadott - {material['name']}",
            'width': material['width'],
            'height': material['height'],
            'color': material['color'],
            'quantity': 1,
            'original_name': material['name']
        }
        results['remainders'].append(remainder_piece)
        
    def create_material_geometry(self, material):
        """Create Shapely geometry from material data"""
        x, y = material['x'], material['y']
        width, height = material['width'], material['height']
        
        # Create rectangle centered at (x, y)
        left = x - width / 2
        bottom = y - height / 2
        right = x + width / 2
        top = y + height / 2
        
        return box(left, bottom, right, top)
        
    def split_remainder(self, remainder_geometry, original_material):
        """Split remainder geometry into usable pieces"""
        remainder_pieces = []
        
        # Handle different geometry types
        if remainder_geometry.geom_type == 'Polygon':
            pieces = [remainder_geometry]
        elif remainder_geometry.geom_type == 'MultiPolygon':
            pieces = list(remainder_geometry.geoms)
        else:
            return remainder_pieces
            
        for i, piece in enumerate(pieces):
            if piece.area > self.tolerance:
                # Calculate bounding box of the piece
                bounds = piece.bounds
                piece_width = bounds[2] - bounds[0]
                piece_height = bounds[3] - bounds[1]
                
                # Only keep pieces that are reasonably sized
                min_size = min(original_material['width'], original_material['height']) * 0.1
                if piece_width >= min_size and piece_height >= min_size:
                    
                    # Create remainder material
                    remainder_piece = {
                        'name': f"Maradék {i+1} - {original_material['name']}",
                        'width': piece_width,
                        'height': piece_height,
                        'color': original_material['color'],
                        'x': (bounds[0] + bounds[2]) / 2,  # Center X
                        'y': (bounds[1] + bounds[3]) / 2,  # Center Y
                        'geometry': piece,
                        'original_name': original_material['name']
                    }
                    remainder_pieces.append(remainder_piece)
                    
        return remainder_pieces
        
    def optimize_material_placement(self, target_shape, available_materials):
        """Optimize placement of materials to cover target shape"""
        # This is a simplified optimization - could be enhanced with more sophisticated algorithms
        
        target_geometry = target_shape['geometry']
        target_bounds = target_geometry.bounds
        
        placements = []
        remaining_area = target_geometry
        
        # Sort materials by area (largest first)
        sorted_materials = sorted(available_materials, 
                                key=lambda m: m['width'] * m['height'], 
                                reverse=True)
        
        for material_type in sorted_materials:
            if material_type['quantity'] <= 0:
                continue
                
            # Try to place materials of this type
            placed_count = 0
            
            while (placed_count < material_type['quantity'] and 
                   remaining_area.area > self.tolerance):
                
                # Find best position for this material
                best_position = self.find_best_position(
                    remaining_area, material_type, target_bounds
                )
                
                if best_position:
                    material_geometry = self.create_material_geometry_at_position(
                        material_type, best_position
                    )
                    
                    # Check intersection
                    intersection = remaining_area.intersection(material_geometry)
                    
                    if intersection.area > self.tolerance:
                        placements.append({
                            'material_type': material_type,
                            'position': best_position,
                            'geometry': material_geometry,
                            'intersection': intersection
                        })
                        
                        # Update remaining area
                        remaining_area = remaining_area.difference(intersection)
                        placed_count += 1
                    else:
                        break
                else:
                    break
                    
        return placements
        
    def find_best_position(self, target_area, material_type, target_bounds):
        """Find best position to place a material"""
        # Simple grid-based search
        width, height = material_type['width'], material_type['height']
        
        # Define search grid
        min_x, min_y, max_x, max_y = target_bounds
        
        step_x = width / 4  # Search every quarter width
        step_y = height / 4  # Search every quarter height
        
        best_position = None
        best_intersection_area = 0
        
        y = min_y + height / 2
        while y <= max_y - height / 2:
            x = min_x + width / 2
            while x <= max_x - width / 2:
                # Create material at this position
                material_geometry = self.create_material_geometry_at_position(
                    material_type, (x, y)
                )
                
                # Calculate intersection
                intersection = target_area.intersection(material_geometry)
                
                if intersection.area > best_intersection_area:
                    best_intersection_area = intersection.area
                    best_position = (x, y)
                    
                x += step_x
            y += step_y
            
        return best_position
        
    def create_material_geometry_at_position(self, material_type, position):
        """Create material geometry at specific position"""
        x, y = position
        width, height = material_type['width'], material_type['height']
        
        left = x - width / 2
        bottom = y - height / 2
        right = x + width / 2
        top = y + height / 2
        
        return box(left, bottom, right, top)
        
    def calculate_cutting_instructions(self, used_materials):
        """Generate cutting instructions for used materials"""
        instructions = []
        
        for used_material in used_materials:
            material = used_material['material']
            intersection = used_material['intersection']
            
            # Get intersection bounds
            bounds = intersection.bounds
            cut_width = bounds[2] - bounds[0]
            cut_height = bounds[3] - bounds[1]
            
            # Calculate cutting coordinates relative to material
            material_bounds = self.create_material_geometry(material).bounds
            
            # Offset from material's bottom-left corner
            offset_x = bounds[0] - material_bounds[0]
            offset_y = bounds[1] - material_bounds[1]
            
            instruction = {
                'material_name': material['name'],
                'original_size': f"{material['width']} x {material['height']}",
                'cut_size': f"{cut_width:.1f} x {cut_height:.1f}",
                'cut_position': f"X: {offset_x:.1f}, Y: {offset_y:.1f}",
                'area_used': intersection.area,
                'area_waste': material['width'] * material['height'] - intersection.area
            }
            
            instructions.append(instruction)
            
        return instructions
        
    def calculate_material_efficiency(self, results):
        """Calculate material usage efficiency"""
        if not results['used_materials']:
            return 0.0
            
        total_material_area = sum(
            um['material']['width'] * um['material']['height'] 
            for um in results['used_materials']
        )
        
        total_used_area = sum(
            um['intersection_area'] 
            for um in results['used_materials']
        )
        
        efficiency = (total_used_area / total_material_area) * 100 if total_material_area > 0 else 0
        return efficiency
        
    def point_in_polygon(self, point, polygon_points):
        """Check if point is inside polygon using ray casting algorithm"""
        x, y = point
        n = len(polygon_points)
        inside = False
        
        p1x, p1y = polygon_points[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon_points[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
            
        return inside
