"""
Geometry utilities for intersection calculations and material processing
"""

import numpy as np
from shapely.geometry import Polygon, Point, box
from shapely.ops import unary_union
import math


class GeometryUtils:
    def __init__(self):
        self.tolerance = 1e-6
        
    def process_intersections(self, selected_shape, canvas_materials):
        """Process intersections between selected shape and materials on canvas - handles complex shapes"""
        if not selected_shape or not canvas_materials:
            return {'used_materials': [], 'remainders': [], 'coverage': 0.0, 'materials_to_keep': [], 'materials_to_remove': []}

        results = {
            'used_materials': [],
            'remainders': [],
            'coverage': 0.0,
            'total_area': 0.0,
            'covered_area': 0.0,
            'materials_to_keep': [],  # Materials that stay on canvas (intersect with shape)
            'materials_to_remove': []  # Materials to remove from canvas
        }

        # Get the target shape geometry (can be complex: L-shaped, U-shaped, etc.)
        target_geometry = selected_shape['geometry']
        target_area = target_geometry.area
        results['total_area'] = target_area

        covered_area = 0.0

        for material in canvas_materials:
            # Create material geometry (can also be complex if it's a remainder piece)
            material_geometry = self.create_material_geometry(material)

            # Calculate intersection - this handles complex shapes properly
            intersection = target_geometry.intersection(material_geometry)

            if intersection.area > self.tolerance:
                # Material intersects with target shape

                # The part that intersects stays on canvas - EXACT shape of intersection
                intersected_material = self.create_intersected_material(material, intersection)
                results['materials_to_keep'].append(intersected_material)
                covered_area += intersection.area

                # Calculate remainder (material - intersection) - EXACT falling off pieces
                remainder = material_geometry.difference(intersection)

                if remainder.area > self.tolerance:
                    # Create remainder pieces - these keep the EXACT complex shape
                    remainder_pieces = self.create_remainder_pieces(remainder, material)
                    results['remainders'].extend(remainder_pieces)

                # Mark original material for removal from canvas
                results['materials_to_remove'].append(material)

            else:
                # Material doesn't intersect - remove it from canvas and add back to materials
                self.add_material_back_to_inventory(material, results)
                results['materials_to_remove'].append(material)

        results['covered_area'] = covered_area
        results['coverage'] = (covered_area / target_area) * 100 if target_area > 0 else 0

        return results

    def create_intersected_material(self, original_material, intersection_geometry):
        """Create material that represents the intersected part - keeps exact shape"""
        bounds = intersection_geometry.bounds
        center_x = (bounds[0] + bounds[2]) / 2
        center_y = (bounds[1] + bounds[3]) / 2

        intersected_material = original_material.copy()
        intersected_material['geometry'] = intersection_geometry
        intersected_material['x'] = center_x
        intersected_material['y'] = center_y
        intersected_material['width'] = bounds[2] - bounds[0]
        intersected_material['height'] = bounds[3] - bounds[1]
        intersected_material['is_complex_shape'] = True
        intersected_material['name'] = f"Felhasznált - {original_material['name']}"

        return intersected_material

    def create_remainder_pieces(self, remainder_geometry, original_material):
        """Create remainder pieces that keep the EXACT complex shape of falling off parts"""
        remainder_pieces = []

        # Handle different geometry types
        if remainder_geometry.geom_type == 'Polygon':
            pieces = [remainder_geometry]
        elif remainder_geometry.geom_type == 'MultiPolygon':
            pieces = list(remainder_geometry.geoms)
        else:
            return remainder_pieces

        for i, piece in enumerate(pieces):
            if piece.area > self.tolerance:
                bounds = piece.bounds
                piece_width = bounds[2] - bounds[0]
                piece_height = bounds[3] - bounds[1]

                # Only keep pieces that are reasonably sized (at least 3cm in both dimensions)
                min_size = 3.0  # 3cm minimum
                if piece_width >= min_size and piece_height >= min_size:

                    remainder_piece = {
                        'name': f"Maradék {i+1} - {original_material['name']}",
                        'width': piece_width,  # Bounding box for display
                        'height': piece_height,
                        'color': original_material['color'],
                        'quantity': 1,
                        'original_name': original_material['name'],
                        'geometry': piece,  # EXACT complex geometry
                        'is_complex_shape': True,  # Always complex for remainders
                        'area': piece.area  # Store actual area
                    }
                    remainder_pieces.append(remainder_piece)

        return remainder_pieces

    def add_material_back_to_inventory(self, material, results):
        """Add a material back to inventory as a remainder piece"""
        # If material doesn't intersect at all, add it back as-is
        remainder_piece = {
            'name': f"Visszaadott - {material['name']}",
            'width': material['width'],
            'height': material['height'],
            'color': material['color'],
            'quantity': 1,
            'original_name': material['name']
        }
        results['remainders'].append(remainder_piece)
        
    def create_material_geometry(self, material):
        """Create Shapely geometry from material data - handles both simple and complex shapes"""

        # If material has complex geometry (remainder piece), use that
        if material.get('is_complex_shape', False) and 'geometry' in material:
            # For complex shapes, we need to position the geometry correctly
            stored_geometry = material['geometry']

            # Get current position
            current_x = material.get('x', 0)
            current_y = material.get('y', 0)

            # Get geometry's current centroid
            centroid = stored_geometry.centroid
            geom_center_x = centroid.x
            geom_center_y = centroid.y

            # Calculate translation needed
            dx = current_x - geom_center_x
            dy = current_y - geom_center_y

            # Translate geometry to current position
            from shapely.affinity import translate
            positioned_geometry = translate(stored_geometry, xoff=dx, yoff=dy)

            return positioned_geometry
        else:
            # Simple rectangular material
            x, y = material['x'], material['y']
            width, height = material['width'], material['height']

            # Create rectangle centered at (x, y)
            left = x - width / 2
            bottom = y - height / 2
            right = x + width / 2
            top = y + height / 2

            return box(left, bottom, right, top)
        


    def extract_rectangular_pieces(self, remainder_geometry, original_material):
        """Extract rectangular pieces from remainder geometry"""
        pieces = []

        # Get the bounding box of the remainder
        bounds = remainder_geometry.bounds
        min_x, min_y, max_x, max_y = bounds

        # Calculate the largest possible rectangles that fit in the remainder
        # This is a simplified approach - could be enhanced with more sophisticated algorithms

        # Try to extract rectangles from different corners
        potential_rectangles = [
            # Top-left aligned rectangle
            (min_x, min_y, max_x, max_y),
            # Try smaller rectangles if the shape is complex
            (min_x, min_y, min_x + (max_x - min_x) * 0.7, min_y + (max_y - min_y) * 0.7),
            (min_x, min_y, min_x + (max_x - min_x) * 0.5, min_y + (max_y - min_y) * 0.5),
        ]

        for rect_bounds in potential_rectangles:
            x1, y1, x2, y2 = rect_bounds
            width = x2 - x1
            height = y2 - y1

            # Skip if too small
            if width < 5.0 or height < 5.0:
                continue

            # Create rectangle geometry
            from shapely.geometry import box
            rect_geom = box(x1, y1, x2, y2)

            # Check if this rectangle fits well within the remainder
            intersection = remainder_geometry.intersection(rect_geom)

            # If the intersection covers at least 80% of the rectangle, it's a good piece
            if intersection.area >= rect_geom.area * 0.8:
                piece = {
                    'name': f"Maradék - {original_material['name']}",
                    'width': width,
                    'height': height,
                    'color': original_material['color'],
                    'quantity': 1,
                    'original_name': original_material['name']
                }
                pieces.append(piece)
                break  # Take the first good rectangle

        # If no good rectangle found, create a piece based on bounding box
        if not pieces:
            bounds = remainder_geometry.bounds
            width = bounds[2] - bounds[0]
            height = bounds[3] - bounds[1]

            if width >= 5.0 and height >= 5.0:
                piece = {
                    'name': f"Maradék - {original_material['name']}",
                    'width': width,
                    'height': height,
                    'color': original_material['color'],
                    'quantity': 1,
                    'original_name': original_material['name']
                }
                pieces.append(piece)

        return pieces
        
    def optimize_material_placement(self, target_shape, available_materials):
        """Optimize placement of materials to cover target shape"""
        # This is a simplified optimization - could be enhanced with more sophisticated algorithms
        
        target_geometry = target_shape['geometry']
        target_bounds = target_geometry.bounds
        
        placements = []
        remaining_area = target_geometry
        
        # Sort materials by area (largest first)
        sorted_materials = sorted(available_materials, 
                                key=lambda m: m['width'] * m['height'], 
                                reverse=True)
        
        for material_type in sorted_materials:
            if material_type['quantity'] <= 0:
                continue
                
            # Try to place materials of this type
            placed_count = 0
            
            while (placed_count < material_type['quantity'] and 
                   remaining_area.area > self.tolerance):
                
                # Find best position for this material
                best_position = self.find_best_position(
                    remaining_area, material_type, target_bounds
                )
                
                if best_position:
                    material_geometry = self.create_material_geometry_at_position(
                        material_type, best_position
                    )
                    
                    # Check intersection
                    intersection = remaining_area.intersection(material_geometry)
                    
                    if intersection.area > self.tolerance:
                        placements.append({
                            'material_type': material_type,
                            'position': best_position,
                            'geometry': material_geometry,
                            'intersection': intersection
                        })
                        
                        # Update remaining area
                        remaining_area = remaining_area.difference(intersection)
                        placed_count += 1
                    else:
                        break
                else:
                    break
                    
        return placements
        
    def find_best_position(self, target_area, material_type, target_bounds):
        """Find best position to place a material"""
        # Simple grid-based search
        width, height = material_type['width'], material_type['height']
        
        # Define search grid
        min_x, min_y, max_x, max_y = target_bounds
        
        step_x = width / 4  # Search every quarter width
        step_y = height / 4  # Search every quarter height
        
        best_position = None
        best_intersection_area = 0
        
        y = min_y + height / 2
        while y <= max_y - height / 2:
            x = min_x + width / 2
            while x <= max_x - width / 2:
                # Create material at this position
                material_geometry = self.create_material_geometry_at_position(
                    material_type, (x, y)
                )
                
                # Calculate intersection
                intersection = target_area.intersection(material_geometry)
                
                if intersection.area > best_intersection_area:
                    best_intersection_area = intersection.area
                    best_position = (x, y)
                    
                x += step_x
            y += step_y
            
        return best_position
        
    def create_material_geometry_at_position(self, material_type, position):
        """Create material geometry at specific position"""
        x, y = position
        width, height = material_type['width'], material_type['height']
        
        left = x - width / 2
        bottom = y - height / 2
        right = x + width / 2
        top = y + height / 2
        
        return box(left, bottom, right, top)
        
    def calculate_cutting_instructions(self, used_materials):
        """Generate cutting instructions for used materials"""
        instructions = []
        
        for used_material in used_materials:
            material = used_material['material']
            intersection = used_material['intersection']
            
            # Get intersection bounds
            bounds = intersection.bounds
            cut_width = bounds[2] - bounds[0]
            cut_height = bounds[3] - bounds[1]
            
            # Calculate cutting coordinates relative to material
            material_bounds = self.create_material_geometry(material).bounds
            
            # Offset from material's bottom-left corner
            offset_x = bounds[0] - material_bounds[0]
            offset_y = bounds[1] - material_bounds[1]
            
            instruction = {
                'material_name': material['name'],
                'original_size': f"{material['width']} x {material['height']}",
                'cut_size': f"{cut_width:.1f} x {cut_height:.1f}",
                'cut_position': f"X: {offset_x:.1f}, Y: {offset_y:.1f}",
                'area_used': intersection.area,
                'area_waste': material['width'] * material['height'] - intersection.area
            }
            
            instructions.append(instruction)
            
        return instructions
        
    def calculate_material_efficiency(self, results):
        """Calculate material usage efficiency"""
        if not results['used_materials']:
            return 0.0
            
        total_material_area = sum(
            um['material']['width'] * um['material']['height'] 
            for um in results['used_materials']
        )
        
        total_used_area = sum(
            um['intersection_area'] 
            for um in results['used_materials']
        )
        
        efficiency = (total_used_area / total_material_area) * 100 if total_material_area > 0 else 0
        return efficiency
        
    def point_in_polygon(self, point, polygon_points):
        """Check if point is inside polygon using ray casting algorithm"""
        x, y = point
        n = len(polygon_points)
        inside = False
        
        p1x, p1y = polygon_points[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon_points[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
            
        return inside
