"""
DXF file handling and closed shape detection
"""

import ezdxf
from ezdxf.math import Vec2
import numpy as np
from shapely.geometry import Polygon, LineString, Point
from shapely.ops import unary_union
import math


class DXFHandler:
    def __init__(self):
        self.entities = []
        self.closed_shapes = []
        self.tolerance = 1e-6
        
    def load_dxf(self, file_path):
        """Load DXF file and extract entities and closed shapes"""
        try:
            # Load DXF document
            doc = ezdxf.readfile(file_path)
            msp = doc.modelspace()
            
            # Extract all entities
            self.entities = []
            lines = []
            arcs = []
            circles = []
            polylines = []
            
            for entity in msp:
                entity_data = self._extract_entity_data(entity)
                if entity_data:
                    self.entities.append(entity_data)
                    
                    # Collect geometric elements for closed shape detection
                    if entity_data['type'] == 'LINE':
                        lines.append(entity_data)
                    elif entity_data['type'] == 'ARC':
                        arcs.append(entity_data)
                    elif entity_data['type'] == 'CIRCLE':
                        circles.append(entity_data)
                    elif entity_data['type'] == 'POLYLINE':
                        polylines.append(entity_data)
            
            # Detect closed shapes
            self.closed_shapes = self._detect_closed_shapes(lines, arcs, circles, polylines)
            
            return self.entities, self.closed_shapes
            
        except Exception as e:
            raise Exception(f"DXF loading error: {str(e)}")
    
    def _extract_entity_data(self, entity):
        """Extract data from DXF entity"""
        entity_type = entity.dxftype()
        
        if entity_type == 'LINE':
            return {
                'type': 'LINE',
                'start': (entity.dxf.start.x, entity.dxf.start.y),
                'end': (entity.dxf.end.x, entity.dxf.end.y),
                'layer': entity.dxf.layer,
                'color': getattr(entity.dxf, 'color', 7)
            }
            
        elif entity_type == 'ARC':
            return {
                'type': 'ARC',
                'center': (entity.dxf.center.x, entity.dxf.center.y),
                'radius': entity.dxf.radius,
                'start_angle': entity.dxf.start_angle,
                'end_angle': entity.dxf.end_angle,
                'layer': entity.dxf.layer,
                'color': getattr(entity.dxf, 'color', 7)
            }
            
        elif entity_type == 'CIRCLE':
            return {
                'type': 'CIRCLE',
                'center': (entity.dxf.center.x, entity.dxf.center.y),
                'radius': entity.dxf.radius,
                'layer': entity.dxf.layer,
                'color': getattr(entity.dxf, 'color', 7)
            }
            
        elif entity_type == 'LWPOLYLINE' or entity_type == 'POLYLINE':
            points = []
            if hasattr(entity, 'get_points'):
                points = [(p[0], p[1]) for p in entity.get_points()]
            elif hasattr(entity, 'vertices'):
                points = [(v.dxf.location.x, v.dxf.location.y) for v in entity.vertices]
            
            return {
                'type': 'POLYLINE',
                'points': points,
                'closed': entity.closed,
                'layer': entity.dxf.layer,
                'color': getattr(entity.dxf, 'color', 7)
            }
            
        return None
    
    def _detect_closed_shapes(self, lines, arcs, circles, polylines):
        """Detect closed shapes from geometric entities"""
        closed_shapes = []
        
        # Add circles as closed shapes
        for circle in circles:
            closed_shapes.append({
                'type': 'circle',
                'geometry': Point(circle['center']).buffer(circle['radius']),
                'entities': [circle],
                'bounds': self._get_circle_bounds(circle)
            })
        
        # Add closed polylines
        for polyline in polylines:
            if polyline['closed'] and len(polyline['points']) >= 3:
                try:
                    polygon = Polygon(polyline['points'])
                    if polygon.is_valid:
                        closed_shapes.append({
                            'type': 'polyline',
                            'geometry': polygon,
                            'entities': [polyline],
                            'bounds': polygon.bounds
                        })
                except:
                    pass
        
        # Try to form closed shapes from lines and arcs
        closed_shapes.extend(self._find_closed_paths(lines, arcs))
        
        return closed_shapes
    
    def _find_closed_paths(self, lines, arcs):
        """Find closed paths from lines and arcs"""
        closed_shapes = []
        all_segments = []
        
        # Convert lines to segments
        for line in lines:
            all_segments.append({
                'start': line['start'],
                'end': line['end'],
                'entity': line,
                'type': 'line'
            })
        
        # Convert arcs to segments (approximate with multiple points)
        for arc in arcs:
            points = self._arc_to_points(arc)
            for i in range(len(points) - 1):
                all_segments.append({
                    'start': points[i],
                    'end': points[i + 1],
                    'entity': arc,
                    'type': 'arc'
                })
        
        # Find closed loops
        used_segments = set()
        
        for i, start_segment in enumerate(all_segments):
            if i in used_segments:
                continue
                
            path = [start_segment]
            current_end = start_segment['end']
            path_segments = {i}
            
            while True:
                next_segment_idx = None
                min_distance = float('inf')
                
                # Find closest connecting segment
                for j, segment in enumerate(all_segments):
                    if j in path_segments:
                        continue
                        
                    dist_to_start = self._point_distance(current_end, segment['start'])
                    dist_to_end = self._point_distance(current_end, segment['end'])
                    
                    if dist_to_start < self.tolerance and dist_to_start < min_distance:
                        next_segment_idx = j
                        min_distance = dist_to_start
                        reverse = False
                    elif dist_to_end < self.tolerance and dist_to_end < min_distance:
                        next_segment_idx = j
                        min_distance = dist_to_end
                        reverse = True
                
                if next_segment_idx is None:
                    break
                    
                next_segment = all_segments[next_segment_idx].copy()
                if reverse:
                    next_segment['start'], next_segment['end'] = next_segment['end'], next_segment['start']
                
                path.append(next_segment)
                path_segments.add(next_segment_idx)
                current_end = next_segment['end']
                
                # Check if we've closed the loop
                if self._point_distance(current_end, start_segment['start']) < self.tolerance:
                    # We have a closed shape
                    if len(path) >= 3:
                        polygon_points = [seg['start'] for seg in path]
                        try:
                            polygon = Polygon(polygon_points)
                            if polygon.is_valid and polygon.area > self.tolerance:
                                entities = [seg['entity'] for seg in path]
                                closed_shapes.append({
                                    'type': 'path',
                                    'geometry': polygon,
                                    'entities': entities,
                                    'bounds': polygon.bounds
                                })
                                used_segments.update(path_segments)
                        except:
                            pass
                    break
        
        return closed_shapes
    
    def _arc_to_points(self, arc, num_points=20):
        """Convert arc to series of points"""
        center = arc['center']
        radius = arc['radius']
        start_angle = math.radians(arc['start_angle'])
        end_angle = math.radians(arc['end_angle'])
        
        # Handle angle wrapping
        if end_angle < start_angle:
            end_angle += 2 * math.pi
            
        angles = np.linspace(start_angle, end_angle, num_points)
        points = []
        
        for angle in angles:
            x = center[0] + radius * math.cos(angle)
            y = center[1] + radius * math.sin(angle)
            points.append((x, y))
            
        return points
    
    def _get_circle_bounds(self, circle):
        """Get bounding box of circle"""
        cx, cy = circle['center']
        r = circle['radius']
        return (cx - r, cy - r, cx + r, cy + r)
    
    def _point_distance(self, p1, p2):
        """Calculate distance between two points"""
        return math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
