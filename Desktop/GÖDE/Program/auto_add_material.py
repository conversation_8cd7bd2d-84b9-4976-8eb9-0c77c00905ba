#!/usr/bin/env python3
"""
Automatically add a test material to verify the system works
"""

import json
import os

def add_test_material():
    """Add a test material directly to the JSON file"""
    
    # Test material
    test_material = {
        'name': '<PERSON><PERSON><PERSON>',
        'width': 120,
        'height': 200,
        'color': '#90EE90',
        'quantity': 5
    }
    
    # Load existing materials or create new list
    materials = []
    if os.path.exists('materials.json'):
        try:
            with open('materials.json', 'r', encoding='utf-8') as f:
                materials = json.load(f)
        except:
            materials = []
    
    # Add test material
    materials.append(test_material)
    
    # Save back to file
    with open('materials.json', 'w', encoding='utf-8') as f:
        json.dump(materials, f, ensure_ascii=False, indent=2)
    
    print("Test material added to materials.json:")
    print(f"  Name: {test_material['name']}")
    print(f"  Size: {test_material['width']}x{test_material['height']} cm")
    print(f"  Quantity: {test_material['quantity']} pieces")
    print(f"  Color: {test_material['color']}")
    print("\nNow restart the main program to see the material in the toolbar!")

if __name__ == "__main__":
    add_test_material()
