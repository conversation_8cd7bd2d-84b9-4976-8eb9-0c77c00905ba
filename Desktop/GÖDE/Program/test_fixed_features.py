#!/usr/bin/env python3
"""
Test the fixed features: exact scaling, no borders, and proper remainder cutting
"""

import tkinter as tk
from material_manager import MaterialManager
from geometry_utils import GeometryUtils
from shapely.geometry import Polygon, box
import json

def test_exact_scaling():
    """Test that toolbar materials have exact same scale as canvas"""
    print("Testing exact scaling between toolbar and canvas...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        test_frame = tk.Frame(root)
        
        class MockCanvasViewer:
            def __init__(self):
                self.zoom_factor = 1.5  # Test with specific zoom
        
        class MockMainApp:
            def __init__(self):
                self.canvas_viewer = MockCanvasViewer()
        
        mock_app = MockMainApp()
        material_manager = MaterialManager(test_frame, mock_app)
        
        # Test material
        test_material = {
            'name': 'Test Material',
            'width': 100,
            'height': 50,
            'color': '#90EE90',
            'quantity': 1
        }
        
        material_manager.material_types = [test_material]
        material_manager.refresh_display()
        
        # Check that canvas widgets use exact zoom scaling
        canvas_widgets = [w for w in material_manager.materials_frame.winfo_children() 
                         if isinstance(w, tk.Canvas)]
        
        if canvas_widgets:
            canvas = canvas_widgets[0]
            expected_width = int(100 * 1.5)  # width * zoom_factor
            expected_height = int(50 * 1.5)   # height * zoom_factor
            
            actual_width = canvas.winfo_reqwidth()
            actual_height = canvas.winfo_reqheight()
            
            print(f"✓ Expected size: {expected_width}x{expected_height}")
            print(f"✓ Actual size: {actual_width}x{actual_height}")
            
            # Allow small tolerance for minimum size constraints
            width_ok = abs(actual_width - expected_width) <= 2
            height_ok = abs(actual_height - expected_height) <= 2
            
            return width_ok and height_ok
        
        return False
        
    except Exception as e:
        print(f"✗ Exact scaling test failed: {e}")
        return False
    finally:
        root.destroy()

def test_no_borders():
    """Test that materials display without gray borders"""
    print("\nTesting no borders in toolbar...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        test_frame = tk.Frame(root)
        
        class MockCanvasViewer:
            def __init__(self):
                self.zoom_factor = 1.0
        
        class MockMainApp:
            def __init__(self):
                self.canvas_viewer = MockCanvasViewer()
        
        mock_app = MockMainApp()
        material_manager = MaterialManager(test_frame, mock_app)
        
        test_material = {
            'name': 'Test Material',
            'width': 80,
            'height': 60,
            'color': '#FFB6C1',
            'quantity': 1
        }
        
        material_manager.material_types = [test_material]
        material_manager.refresh_display()
        
        # Check canvas properties
        canvas_widgets = [w for w in material_manager.materials_frame.winfo_children() 
                         if isinstance(w, tk.Canvas)]
        
        if canvas_widgets:
            canvas = canvas_widgets[0]
            
            # Check border properties
            highlight_thickness = canvas.cget('highlightthickness')
            border_width = canvas.cget('borderwidth')
            relief = canvas.cget('relief')
            bg_color = canvas.cget('bg')
            
            print(f"✓ Highlight thickness: {highlight_thickness} (should be 0)")
            print(f"✓ Border width: {border_width} (should be 0)")
            print(f"✓ Relief: {relief} (should be flat)")
            print(f"✓ Background: {bg_color} (should be white)")
            
            return (highlight_thickness == 0 and 
                   border_width == 0 and 
                   relief == 'flat' and
                   bg_color == 'white')
        
        return False
        
    except Exception as e:
        print(f"✗ No borders test failed: {e}")
        return False
    finally:
        root.destroy()

def test_proper_remainder_cutting():
    """Test that remainders are properly cut (only falling off pieces)"""
    print("\nTesting proper remainder cutting...")
    
    try:
        geom_utils = GeometryUtils()
        
        # Create a target shape (small rectangle)
        target_shape = {
            'geometry': Polygon([(20, 20), (80, 20), (80, 80), (20, 80)]),
            'type': 'test'
        }
        
        # Create a larger material that overlaps
        test_material = {
            'x': 50,  # Centered
            'y': 50,
            'width': 100,  # Larger than target
            'height': 100,
            'name': 'Test Material',
            'color': '#FF0000'
        }
        
        # Process intersections
        results = geom_utils.process_intersections(target_shape, [test_material])
        
        print(f"✓ Coverage: {results['coverage']:.1f}%")
        print(f"✓ Materials to keep: {len(results['materials_to_keep'])}")
        print(f"✓ Materials to remove: {len(results['materials_to_remove'])}")
        print(f"✓ Remainders: {len(results['remainders'])}")
        
        # Check remainders
        if results['remainders']:
            for i, remainder in enumerate(results['remainders']):
                print(f"  Remainder {i+1}: {remainder['name']}")
                print(f"    Size: {remainder['width']:.1f}x{remainder['height']:.1f} cm")
                print(f"    Complex shape: {remainder.get('is_complex_shape', False)}")
                
                # Check if it has geometry (for complex shapes)
                if 'geometry' in remainder:
                    geom = remainder['geometry']
                    print(f"    Geometry area: {geom.area:.1f}")
                    print(f"    Geometry type: {geom.geom_type}")
        
        # Should have remainders (the falling off parts)
        return len(results['remainders']) > 0
        
    except Exception as e:
        print(f"✗ Remainder cutting test failed: {e}")
        return False

def test_complex_shape_display():
    """Test that complex shaped remainders can be displayed"""
    print("\nTesting complex shape display...")
    
    try:
        # Create an L-shaped geometry
        l_shape = Polygon([
            (0, 0), (60, 0), (60, 30), (30, 30), (30, 60), (0, 60)
        ])
        
        # Create a complex material
        complex_material = {
            'name': 'L-shaped Remainder',
            'width': 60,  # Bounding box width
            'height': 60,  # Bounding box height
            'color': '#FFFF00',
            'quantity': 1,
            'geometry': l_shape,
            'is_complex_shape': True
        }
        
        print(f"✓ Complex material created: {complex_material['name']}")
        print(f"✓ Bounding box: {complex_material['width']}x{complex_material['height']}")
        print(f"✓ Geometry type: {l_shape.geom_type}")
        print(f"✓ Geometry area: {l_shape.area}")
        print(f"✓ Is complex shape: {complex_material['is_complex_shape']}")
        
        # Test that it can be serialized (for saving)
        import json
        try:
            # Remove geometry for JSON test (geometry can't be serialized directly)
            json_material = complex_material.copy()
            del json_material['geometry']
            json_str = json.dumps([json_material], ensure_ascii=False, indent=2)
            print("✓ Complex material can be serialized (without geometry)")
        except Exception as e:
            print(f"✗ Serialization failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Complex shape display test failed: {e}")
        return False

def main():
    """Run all fixed feature tests"""
    print("Fixed Features Test Suite")
    print("=" * 50)
    
    tests = [
        ("Exact Scaling", test_exact_scaling),
        ("No Borders", test_no_borders),
        ("Proper Remainder Cutting", test_proper_remainder_cutting),
        ("Complex Shape Display", test_complex_shape_display)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED")
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes work correctly!")
        print("\nFixed issues:")
        print("- Toolbar materials now have exact same scale as canvas")
        print("- No gray borders around materials in toolbar")
        print("- Remainders are properly cut (only falling off pieces)")
        print("- Complex shapes (L-shaped, etc.) are supported")
    else:
        print("⚠️  Some fixes need attention.")
        
    return passed == total

if __name__ == "__main__":
    main()
