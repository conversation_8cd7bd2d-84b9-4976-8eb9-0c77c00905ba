#!/usr/bin/env python3
"""
Test script to verify the main components of the drywall optimization program
"""

import sys
import os

def test_imports():
    """Test if all modules can be imported"""
    print("Testing imports...")
    
    try:
        import tkinter as tk
        print("✓ tkinter imported successfully")
    except ImportError as e:
        print(f"✗ tkinter import failed: {e}")
        return False
        
    try:
        import ezdxf
        print("✓ ezdxf imported successfully")
    except ImportError as e:
        print(f"✗ ezdxf import failed: {e}")
        return False
        
    try:
        import numpy as np
        print("✓ numpy imported successfully")
    except ImportError as e:
        print(f"✗ numpy import failed: {e}")
        return False
        
    try:
        from shapely.geometry import Polygon, Point
        print("✓ shapely imported successfully")
    except ImportError as e:
        print(f"✗ shapely import failed: {e}")
        return False
        
    try:
        from dxf_handler import DXFHandler
        print("✓ dxf_handler imported successfully")
    except ImportError as e:
        print(f"✗ dxf_handler import failed: {e}")
        return False
        
    try:
        from canvas_viewer import CanvasViewer
        print("✓ canvas_viewer imported successfully")
    except ImportError as e:
        print(f"✗ canvas_viewer import failed: {e}")
        return False
        
    try:
        from material_manager import MaterialManager
        print("✓ material_manager imported successfully")
    except ImportError as e:
        print(f"✗ material_manager import failed: {e}")
        return False
        
    try:
        from geometry_utils import GeometryUtils
        print("✓ geometry_utils imported successfully")
    except ImportError as e:
        print(f"✗ geometry_utils import failed: {e}")
        return False
        
    return True

def test_dxf_handler():
    """Test DXF handler functionality"""
    print("\nTesting DXF handler...")
    
    try:
        from dxf_handler import DXFHandler
        
        # Check if test DXF file exists
        if not os.path.exists('test_shapes.dxf'):
            print("✗ test_shapes.dxf not found. Run create_test_dxf.py first.")
            return False
            
        dxf_handler = DXFHandler()
        entities, closed_shapes = dxf_handler.load_dxf('test_shapes.dxf')
        
        print(f"✓ DXF loaded: {len(entities)} entities, {len(closed_shapes)} closed shapes")
        
        if len(closed_shapes) > 0:
            print("✓ Closed shapes detected successfully")
            for i, shape in enumerate(closed_shapes):
                print(f"  Shape {i+1}: {shape['type']}, bounds: {shape['bounds']}")
        else:
            print("✗ No closed shapes detected")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ DXF handler test failed: {e}")
        return False

def test_geometry_utils():
    """Test geometry utilities"""
    print("\nTesting geometry utilities...")
    
    try:
        from geometry_utils import GeometryUtils
        from shapely.geometry import Polygon
        
        geom_utils = GeometryUtils()
        
        # Create test shapes
        target_shape = {
            'geometry': Polygon([(0, 0), (100, 0), (100, 100), (0, 100)]),
            'type': 'test'
        }
        
        test_material = {
            'x': 50,
            'y': 50,
            'width': 60,
            'height': 60,
            'name': 'Test Material',
            'color': '#FF0000'
        }
        
        # Test intersection processing
        results = geom_utils.process_intersections(target_shape, [test_material])
        
        print(f"✓ Intersection processing: {results['coverage']:.1f}% coverage")
        print(f"✓ Used materials: {len(results['used_materials'])}")
        print(f"✓ Remainders: {len(results['remainders'])}")
        
        return True
        
    except Exception as e:
        print(f"✗ Geometry utils test failed: {e}")
        return False

def test_material_data():
    """Test material data handling"""
    print("\nTesting material data...")
    
    try:
        import json
        
        # Test material structure
        test_material = {
            'name': 'Test Gipszkarton',
            'width': 120,
            'height': 200,
            'color': '#90EE90',
            'quantity': 10
        }
        
        # Test JSON serialization
        json_str = json.dumps([test_material], ensure_ascii=False, indent=2)
        loaded_materials = json.loads(json_str)
        
        print("✓ Material JSON serialization works")
        print(f"✓ Test material: {loaded_materials[0]['name']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Material data test failed: {e}")
        return False

def test_file_structure():
    """Test if all required files exist"""
    print("\nTesting file structure...")
    
    required_files = [
        'main.py',
        'dxf_handler.py',
        'canvas_viewer.py',
        'material_manager.py',
        'geometry_utils.py',
        'requirements.txt'
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file} exists")
        else:
            print(f"✗ {file} missing")
            all_exist = False
            
    return all_exist

def main():
    """Run all tests"""
    print("Drywall Optimization Program - Test Suite")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Imports", test_imports),
        ("DXF Handler", test_dxf_handler),
        ("Geometry Utils", test_geometry_utils),
        ("Material Data", test_material_data)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 20)
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED")
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The program should work correctly.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
