#!/usr/bin/env python3
"""
Test script to verify material adding functionality
"""

import tkinter as tk
from material_manager import MaterialManager, MaterialDialog
import json
import os

def test_material_dialog():
    """Test the material dialog functionality"""
    print("Testing material dialog...")
    
    # Create a test root window
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    try:
        # Create a test dialog
        dialog = MaterialDialog(root, "Test Dialog")
        
        # Simulate filling in the dialog
        dialog.name_var.set("Test Gipszkarton")
        dialog.width_var.set("120")
        dialog.height_var.set("200")
        dialog.quantity_var.set("5")
        dialog.color_var.set("#90EE90")
        
        # Simulate clicking OK
        dialog.ok_clicked()
        
        if dialog.result:
            print("✓ Dialog created result successfully")
            print(f"  Name: {dialog.result['name']}")
            print(f"  Size: {dialog.result['width']}x{dialog.result['height']}")
            print(f"  Quantity: {dialog.result['quantity']}")
            print(f"  Color: {dialog.result['color']}")
            return True
        else:
            print("✗ Dialog did not create result")
            return False
            
    except Exception as e:
        print(f"✗ Dialog test failed: {e}")
        return False
    finally:
        root.destroy()

def test_material_manager():
    """Test the material manager functionality"""
    print("\nTesting material manager...")
    
    # Clean up any existing materials file
    if os.path.exists('materials.json'):
        os.remove('materials.json')
    
    # Create a test root window
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    try:
        # Create a test frame
        test_frame = tk.Frame(root)
        
        # Create a mock main app
        class MockMainApp:
            def __init__(self):
                pass
        
        mock_app = MockMainApp()
        
        # Create material manager
        material_manager = MaterialManager(test_frame, mock_app)
        
        # Test adding a material programmatically
        test_material = {
            'name': 'Test Material',
            'width': 100,
            'height': 150,
            'color': '#FF0000',
            'quantity': 3
        }
        
        material_manager.material_types.append(test_material)
        material_manager.save_materials()
        
        print("✓ Material added programmatically")
        
        # Test loading materials
        material_manager.load_materials()
        
        if len(material_manager.material_types) > 0:
            print(f"✓ Materials loaded: {len(material_manager.material_types)} types")
            for material in material_manager.material_types:
                print(f"  - {material['name']}: {material['quantity']} pieces")
            return True
        else:
            print("✗ No materials loaded")
            return False
            
    except Exception as e:
        print(f"✗ Material manager test failed: {e}")
        return False
    finally:
        root.destroy()

def test_json_persistence():
    """Test JSON file persistence"""
    print("\nTesting JSON persistence...")
    
    try:
        # Create test materials
        test_materials = [
            {
                'name': 'Teszt Anyag 1',
                'width': 120,
                'height': 200,
                'color': '#90EE90',
                'quantity': 5
            },
            {
                'name': 'Teszt Anyag 2',
                'width': 150,
                'height': 250,
                'color': '#FFB6C1',
                'quantity': 3
            }
        ]
        
        # Save to JSON
        with open('test_materials.json', 'w', encoding='utf-8') as f:
            json.dump(test_materials, f, ensure_ascii=False, indent=2)
        
        print("✓ Materials saved to JSON")
        
        # Load from JSON
        with open('test_materials.json', 'r', encoding='utf-8') as f:
            loaded_materials = json.load(f)
        
        if len(loaded_materials) == 2:
            print("✓ Materials loaded from JSON")
            print(f"  Material 1: {loaded_materials[0]['name']}")
            print(f"  Material 2: {loaded_materials[1]['name']}")
            
            # Clean up
            os.remove('test_materials.json')
            return True
        else:
            print("✗ Incorrect number of materials loaded")
            return False
            
    except Exception as e:
        print(f"✗ JSON persistence test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Material Adding Test Suite")
    print("=" * 40)
    
    tests = [
        ("Material Dialog", test_material_dialog),
        ("Material Manager", test_material_manager),
        ("JSON Persistence", test_json_persistence)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED")
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Material adding should work.")
    else:
        print("⚠️  Some tests failed. Check the issues above.")
        
    return passed == total

if __name__ == "__main__":
    main()
