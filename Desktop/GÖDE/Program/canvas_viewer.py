"""
Canvas viewer with AutoCAD-like navigation and drag & drop functionality
"""

import tkinter as tk
from tkinter import ttk
import math
from shapely.geometry import Point, Polygon
import numpy as np


class CanvasViewer:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        
        # Canvas properties
        self.zoom_factor = 1.0
        self.pan_x = 0
        self.pan_y = 0
        self.selection_mode = False
        
        # Data
        self.entities = []
        self.closed_shapes = []
        self.canvas_materials = []
        self.selected_shape = None
        
        # Mouse interaction
        self.last_mouse_x = 0
        self.last_mouse_y = 0
        self.dragging_material = None
        self.drag_offset_x = 0
        self.drag_offset_y = 0
        
        self.setup_ui()
        self.setup_bindings()
        
    def setup_ui(self):
        """Setup canvas UI"""
        # Create canvas with scrollbars
        canvas_frame = ttk.Frame(self.parent)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # Canvas
        self.canvas = tk.Canvas(canvas_frame, bg='white', cursor='cross')
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # Status bar for coordinates
        self.coord_label = ttk.Label(self.parent, text="X: 0, Y: 0")
        self.coord_label.pack(side=tk.BOTTOM, anchor=tk.W)
        
    def setup_bindings(self):
        """Setup mouse and keyboard bindings"""
        # Mouse events
        self.canvas.bind('<Button-1>', self.on_left_click)
        self.canvas.bind('<B1-Motion>', self.on_left_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_left_release)
        
        self.canvas.bind('<Button-2>', self.on_middle_click)  # Pan start
        self.canvas.bind('<B2-Motion>', self.on_middle_drag)  # Pan
        
        self.canvas.bind('<Button-3>', self.on_right_click)  # Context menu
        
        # Mouse wheel for zoom
        self.canvas.bind('<MouseWheel>', self.on_mouse_wheel)
        self.canvas.bind('<Button-4>', self.on_mouse_wheel)  # Linux
        self.canvas.bind('<Button-5>', self.on_mouse_wheel)  # Linux
        
        # Mouse motion for coordinates
        self.canvas.bind('<Motion>', self.on_mouse_motion)
        
        # Keyboard
        self.canvas.bind('<Key>', self.on_key_press)
        self.canvas.focus_set()
        
    def load_entities(self, entities, closed_shapes):
        """Load DXF entities and closed shapes"""
        self.entities = entities
        self.closed_shapes = closed_shapes
        self.canvas_materials = []
        self.selected_shape = None
        
        # Calculate bounds for auto-fit
        self.calculate_bounds()
        
        # Auto-fit to view
        self.fit_to_view()
        
        # Redraw
        self.redraw()
        
    def calculate_bounds(self):
        """Calculate bounds of all entities"""
        if not self.entities and not self.closed_shapes:
            self.bounds = (-100, -100, 100, 100)
            return
            
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        # Check entity bounds
        for entity in self.entities:
            if entity['type'] == 'LINE':
                min_x = min(min_x, entity['start'][0], entity['end'][0])
                max_x = max(max_x, entity['start'][0], entity['end'][0])
                min_y = min(min_y, entity['start'][1], entity['end'][1])
                max_y = max(max_y, entity['start'][1], entity['end'][1])
                
            elif entity['type'] == 'CIRCLE':
                cx, cy = entity['center']
                r = entity['radius']
                min_x = min(min_x, cx - r)
                max_x = max(max_x, cx + r)
                min_y = min(min_y, cy - r)
                max_y = max(max_y, cy + r)
                
            elif entity['type'] == 'POLYLINE':
                for point in entity['points']:
                    min_x = min(min_x, point[0])
                    max_x = max(max_x, point[0])
                    min_y = min(min_y, point[1])
                    max_y = max(max_y, point[1])
        
        # Check closed shape bounds
        for shape in self.closed_shapes:
            bounds = shape['bounds']
            min_x = min(min_x, bounds[0])
            min_y = min(min_y, bounds[1])
            max_x = max(max_x, bounds[2])
            max_y = max(max_y, bounds[3])
            
        # Add some padding
        padding = max(max_x - min_x, max_y - min_y) * 0.1
        self.bounds = (min_x - padding, min_y - padding, 
                      max_x + padding, max_y + padding)
    
    def fit_to_view(self):
        """Fit drawing to canvas view"""
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            self.canvas.after(100, self.fit_to_view)
            return
            
        bounds_width = self.bounds[2] - self.bounds[0]
        bounds_height = self.bounds[3] - self.bounds[1]
        
        if bounds_width == 0 or bounds_height == 0:
            return
            
        # Calculate zoom to fit
        zoom_x = canvas_width / bounds_width
        zoom_y = canvas_height / bounds_height
        self.zoom_factor = min(zoom_x, zoom_y) * 0.9  # 90% to leave some margin
        
        # Center the view
        bounds_center_x = (self.bounds[0] + self.bounds[2]) / 2
        bounds_center_y = (self.bounds[1] + self.bounds[3]) / 2
        
        self.pan_x = canvas_width / 2 - bounds_center_x * self.zoom_factor
        self.pan_y = canvas_height / 2 + bounds_center_y * self.zoom_factor  # Y is flipped
        
    def world_to_screen(self, world_x, world_y):
        """Convert world coordinates to screen coordinates"""
        screen_x = world_x * self.zoom_factor + self.pan_x
        screen_y = -world_y * self.zoom_factor + self.pan_y  # Y is flipped
        return screen_x, screen_y
        
    def screen_to_world(self, screen_x, screen_y):
        """Convert screen coordinates to world coordinates"""
        world_x = (screen_x - self.pan_x) / self.zoom_factor
        world_y = -(screen_y - self.pan_y) / self.zoom_factor  # Y is flipped
        return world_x, world_y
        
    def redraw(self):
        """Redraw all entities on canvas"""
        self.canvas.delete("all")
        
        # Draw DXF entities
        self.draw_entities()
        
        # Draw closed shapes (if in selection mode)
        if self.selection_mode:
            self.draw_closed_shapes()
            
        # Draw selected shape highlight
        if self.selected_shape:
            self.draw_selected_shape()
            
        # Draw canvas materials
        self.draw_canvas_materials()
        
    def draw_entities(self):
        """Draw DXF entities"""
        for entity in self.entities:
            if entity['type'] == 'LINE':
                self.draw_line(entity)
            elif entity['type'] == 'CIRCLE':
                self.draw_circle(entity)
            elif entity['type'] == 'ARC':
                self.draw_arc(entity)
            elif entity['type'] == 'POLYLINE':
                self.draw_polyline(entity)
                
    def draw_line(self, line):
        """Draw line entity"""
        x1, y1 = self.world_to_screen(*line['start'])
        x2, y2 = self.world_to_screen(*line['end'])
        
        color = self.get_entity_color(line)
        self.canvas.create_line(x1, y1, x2, y2, fill=color, width=1, tags="entity")
        
    def draw_circle(self, circle):
        """Draw circle entity"""
        cx, cy = self.world_to_screen(*circle['center'])
        r = circle['radius'] * self.zoom_factor
        
        color = self.get_entity_color(circle)
        self.canvas.create_oval(cx-r, cy-r, cx+r, cy+r, 
                               outline=color, width=1, tags="entity")
        
    def draw_arc(self, arc):
        """Draw arc entity (simplified as line segments)"""
        # Convert arc to points and draw as connected lines
        points = self._arc_to_points(arc)
        color = self.get_entity_color(arc)
        
        for i in range(len(points) - 1):
            x1, y1 = self.world_to_screen(*points[i])
            x2, y2 = self.world_to_screen(*points[i + 1])
            self.canvas.create_line(x1, y1, x2, y2, fill=color, width=1, tags="entity")
            
    def draw_polyline(self, polyline):
        """Draw polyline entity"""
        if len(polyline['points']) < 2:
            return
            
        color = self.get_entity_color(polyline)
        points = []
        
        for point in polyline['points']:
            x, y = self.world_to_screen(*point)
            points.extend([x, y])
            
        if polyline['closed']:
            # Close the polyline
            x, y = self.world_to_screen(*polyline['points'][0])
            points.extend([x, y])
            
        self.canvas.create_line(points, fill=color, width=1, tags="entity")
        
    def draw_closed_shapes(self):
        """Draw closed shapes with highlight"""
        for i, shape in enumerate(self.closed_shapes):
            if shape['type'] == 'circle':
                # Draw circle outline
                entity = shape['entities'][0]
                cx, cy = self.world_to_screen(*entity['center'])
                r = entity['radius'] * self.zoom_factor
                self.canvas.create_oval(cx-r, cy-r, cx+r, cy+r, 
                                       outline='red', width=2, tags=f"shape_{i}")
            else:
                # Draw polygon outline
                bounds = shape['bounds']
                coords = []
                
                # Get polygon exterior coordinates
                if hasattr(shape['geometry'], 'exterior'):
                    for x, y in shape['geometry'].exterior.coords:
                        sx, sy = self.world_to_screen(x, y)
                        coords.extend([sx, sy])
                        
                if len(coords) >= 6:  # At least 3 points
                    self.canvas.create_line(coords, fill='red', width=2, tags=f"shape_{i}")
                    
    def draw_selected_shape(self):
        """Draw selected shape with special highlight"""
        if not self.selected_shape:
            return
            
        shape = self.selected_shape
        if shape['type'] == 'circle':
            entity = shape['entities'][0]
            cx, cy = self.world_to_screen(*entity['center'])
            r = entity['radius'] * self.zoom_factor
            self.canvas.create_oval(cx-r, cy-r, cx+r, cy+r, 
                                   outline='blue', width=3, tags="selected_shape")
        else:
            coords = []
            if hasattr(shape['geometry'], 'exterior'):
                for x, y in shape['geometry'].exterior.coords:
                    sx, sy = self.world_to_screen(x, y)
                    coords.extend([sx, sy])
                    
            if len(coords) >= 6:
                self.canvas.create_line(coords, fill='blue', width=3, tags="selected_shape")
                
    def draw_canvas_materials(self):
        """Draw materials placed on canvas"""
        for material in self.canvas_materials:
            self.draw_material(material)
            
    def draw_material(self, material):
        """Draw a single material on canvas"""
        if material.get('is_complex_shape', False) and 'geometry' in material:
            # Draw complex shaped material
            self.draw_complex_material(material)
        else:
            # Draw simple rectangular material
            x, y = self.world_to_screen(material['x'], material['y'])
            w = material['width'] * self.zoom_factor
            h = material['height'] * self.zoom_factor

            # Draw rectangle
            rect_id = self.canvas.create_rectangle(
                x - w/2, y - h/2, x + w/2, y + h/2,
                outline=material['color'], fill=material['color'],
                stipple='gray25', width=2, tags="material"
            )

            # Store reference
            material['canvas_id'] = rect_id

            # Draw label
            label = f"{material['name']}\n{material['width']}x{material['height']}"
            self.canvas.create_text(x, y, text=label, tags="material_label")

    def draw_complex_material(self, material):
        """Draw complex shaped material on canvas"""
        try:
            geometry = material['geometry']

            if hasattr(geometry, 'exterior'):
                coords = list(geometry.exterior.coords)
                canvas_coords = []

                for x, y in coords:
                    screen_x, screen_y = self.world_to_screen(x, y)
                    canvas_coords.extend([screen_x, screen_y])

                if len(canvas_coords) >= 6:  # At least 3 points
                    poly_id = self.canvas.create_polygon(
                        canvas_coords,
                        outline=material['color'],
                        fill=material['color'],
                        stipple='gray25',
                        width=2,
                        tags="material"
                    )

                    # Store reference
                    material['canvas_id'] = poly_id

                    # Draw label at centroid
                    bounds = geometry.bounds
                    center_x = (bounds[0] + bounds[2]) / 2
                    center_y = (bounds[1] + bounds[3]) / 2
                    screen_x, screen_y = self.world_to_screen(center_x, center_y)

                    label = f"{material['name']}\n{material['width']:.0f}x{material['height']:.0f}"
                    self.canvas.create_text(screen_x, screen_y, text=label, tags="material_label")

        except Exception as e:
            print(f"Error drawing complex material: {e}")
            # Fallback to rectangle
            x, y = self.world_to_screen(material.get('x', 0), material.get('y', 0))
            w = material['width'] * self.zoom_factor
            h = material['height'] * self.zoom_factor

            rect_id = self.canvas.create_rectangle(
                x - w/2, y - h/2, x + w/2, y + h/2,
                outline=material['color'], fill=material['color'],
                stipple='gray25', width=2, tags="material"
            )
            material['canvas_id'] = rect_id
        
    def get_entity_color(self, entity):
        """Get color for entity based on AutoCAD color index"""
        color_map = {
            1: 'red', 2: 'yellow', 3: 'green', 4: 'cyan',
            5: 'blue', 6: 'magenta', 7: 'black', 8: 'gray'
        }
        return color_map.get(entity.get('color', 7), 'black')
        
    def _arc_to_points(self, arc, num_points=20):
        """Convert arc to series of points"""
        center = arc['center']
        radius = arc['radius']
        start_angle = math.radians(arc['start_angle'])
        end_angle = math.radians(arc['end_angle'])
        
        if end_angle < start_angle:
            end_angle += 2 * math.pi
            
        angles = np.linspace(start_angle, end_angle, num_points)
        points = []
        
        for angle in angles:
            x = center[0] + radius * math.cos(angle)
            y = center[1] + radius * math.sin(angle)
            points.append((x, y))
            
        return points

    # Event handlers
    def on_left_click(self, event):
        """Handle left mouse click"""
        self.last_mouse_x = event.x
        self.last_mouse_y = event.y

        if self.selection_mode:
            # Shape selection mode
            world_x, world_y = self.screen_to_world(event.x, event.y)
            clicked_shape = self.find_shape_at_point(world_x, world_y)

            if clicked_shape:
                self.selected_shape = clicked_shape
                self.selection_mode = False
                self.main_app.on_shape_selected(clicked_shape)
                self.redraw()
        else:
            # Check if clicking on a material for dragging
            clicked_material = self.find_material_at_point(event.x, event.y)
            if clicked_material:
                self.dragging_material = clicked_material
                world_x, world_y = self.screen_to_world(event.x, event.y)
                self.drag_offset_x = world_x - clicked_material['x']
                self.drag_offset_y = world_y - clicked_material['y']

    def on_left_drag(self, event):
        """Handle left mouse drag"""
        if self.dragging_material:
            # Drag material
            world_x, world_y = self.screen_to_world(event.x, event.y)
            self.dragging_material['x'] = world_x - self.drag_offset_x
            self.dragging_material['y'] = world_y - self.drag_offset_y
            self.redraw()
        else:
            # Pan view
            dx = event.x - self.last_mouse_x
            dy = event.y - self.last_mouse_y
            self.pan_x += dx
            self.pan_y += dy
            self.last_mouse_x = event.x
            self.last_mouse_y = event.y
            self.redraw()

    def on_left_release(self, event):
        """Handle left mouse release"""
        self.dragging_material = None

    def on_middle_click(self, event):
        """Handle middle mouse click (pan start)"""
        self.last_mouse_x = event.x
        self.last_mouse_y = event.y

    def on_middle_drag(self, event):
        """Handle middle mouse drag (pan)"""
        dx = event.x - self.last_mouse_x
        dy = event.y - self.last_mouse_y
        self.pan_x += dx
        self.pan_y += dy
        self.last_mouse_x = event.x
        self.last_mouse_y = event.y
        self.redraw()

    def on_right_click(self, event):
        """Handle right mouse click (context menu)"""
        # TODO: Implement context menu
        pass

    def on_mouse_wheel(self, event):
        """Handle mouse wheel (zoom)"""
        # Get mouse position in world coordinates before zoom
        world_x, world_y = self.screen_to_world(event.x, event.y)

        # Determine zoom direction
        if event.delta > 0 or event.num == 4:
            zoom_factor = 1.1
        else:
            zoom_factor = 0.9

        # Apply zoom
        self.zoom_factor *= zoom_factor

        # Adjust pan to keep mouse position fixed
        new_screen_x, new_screen_y = self.world_to_screen(world_x, world_y)
        self.pan_x += event.x - new_screen_x
        self.pan_y += event.y - new_screen_y

        self.redraw()

        # Notify material manager about zoom change
        if hasattr(self.main_app, 'material_manager'):
            self.main_app.material_manager.update_display_scale()

    def on_mouse_motion(self, event):
        """Handle mouse motion (update coordinates)"""
        world_x, world_y = self.screen_to_world(event.x, event.y)
        self.main_app.status_label.config(text=f"X: {world_x:.2f}, Y: {world_y:.2f}")

    def on_key_press(self, event):
        """Handle key press"""
        if event.keysym == 'Escape':
            self.selection_mode = False
            self.selected_shape = None
            self.redraw()

    # Utility methods
    def toggle_selection_mode(self):
        """Toggle shape selection mode"""
        self.selection_mode = not self.selection_mode
        if self.selection_mode:
            self.canvas.config(cursor='hand2')
        else:
            self.canvas.config(cursor='cross')
        self.redraw()

    def find_shape_at_point(self, world_x, world_y):
        """Find closed shape at given world coordinates"""
        point = Point(world_x, world_y)

        for shape in self.closed_shapes:
            if shape['geometry'].contains(point):
                return shape
        return None

    def find_material_at_point(self, screen_x, screen_y):
        """Find material at given screen coordinates"""
        for material in self.canvas_materials:
            mat_screen_x, mat_screen_y = self.world_to_screen(material['x'], material['y'])
            w = material['width'] * self.zoom_factor / 2
            h = material['height'] * self.zoom_factor / 2

            if (mat_screen_x - w <= screen_x <= mat_screen_x + w and
                mat_screen_y - h <= screen_y <= mat_screen_y + h):
                return material
        return None

    def add_material_to_canvas(self, material_type, screen_x, screen_y):
        """Add material to canvas at screen coordinates"""
        # Only allow adding materials if a shape is selected
        if not self.selected_shape:
            return False

        world_x, world_y = self.screen_to_world(screen_x, screen_y)

        material = {
            'name': material_type['name'],
            'width': material_type['width'],
            'height': material_type['height'],
            'color': material_type['color'],
            'x': world_x,
            'y': world_y,
            'original_material': material_type
        }

        self.canvas_materials.append(material)
        self.redraw()
        return True

    def get_canvas_materials(self):
        """Get all materials currently on canvas"""
        return self.canvas_materials.copy()

    def update_after_processing(self, results):
        """Update canvas after material processing"""
        # Remove materials that should be removed
        materials_to_remove = results.get('materials_to_remove', [])
        self.canvas_materials = [m for m in self.canvas_materials if m not in materials_to_remove]

        # Add materials that should stay (cut to shape)
        materials_to_keep = results.get('materials_to_keep', [])
        self.canvas_materials.extend(materials_to_keep)

        self.redraw()
