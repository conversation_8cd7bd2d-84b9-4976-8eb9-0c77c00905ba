#!/usr/bin/env python3
"""
Test the new visual-only material display and improved cutting logic
"""

import tkinter as tk
from material_manager import MaterialManager
from geometry_utils import GeometryUtils
from shapely.geometry import Polygon, box
import json

def test_visual_only_display():
    """Test that materials display as visual shapes only"""
    print("Testing visual-only material display...")
    
    # Create test root
    root = tk.Tk()
    root.withdraw()
    
    try:
        # Create test frame
        test_frame = tk.Frame(root)
        
        # Mock main app with canvas viewer
        class MockCanvasViewer:
            def __init__(self):
                self.zoom_factor = 1.0
        
        class MockMainApp:
            def __init__(self):
                self.canvas_viewer = MockCanvasViewer()
        
        mock_app = MockMainApp()
        
        # Create material manager
        material_manager = MaterialManager(test_frame, mock_app)
        
        # Add test materials
        test_materials = [
            {
                'name': 'Test Material 1',
                'width': 120,
                'height': 200,
                'color': '#90EE90',
                'quantity': 3
            },
            {
                'name': 'Test Material 2',
                'width': 150,
                'height': 100,
                'color': '#FFB6C1',
                'quantity': 2
            }
        ]
        
        material_manager.material_types = test_materials
        material_manager.refresh_display()
        
        # Count widgets - should be 5 canvas widgets (3 + 2 pieces)
        canvas_widgets = [w for w in material_manager.materials_frame.winfo_children() 
                         if isinstance(w, tk.Canvas)]
        
        print(f"✓ Created {len(canvas_widgets)} visual pieces (expected: 5)")
        
        # Test zoom scaling
        mock_app.canvas_viewer.zoom_factor = 2.0
        material_manager.update_display_scale()
        
        print("✓ Zoom scaling update works")
        
        return len(canvas_widgets) == 5
        
    except Exception as e:
        print(f"✗ Visual display test failed: {e}")
        return False
    finally:
        root.destroy()

def test_improved_cutting_logic():
    """Test the improved cutting logic that only keeps 'falling off' pieces"""
    print("\nTesting improved cutting logic...")
    
    try:
        geom_utils = GeometryUtils()
        
        # Create a target shape (rectangle)
        target_shape = {
            'geometry': Polygon([(0, 0), (100, 0), (100, 100), (0, 100)]),
            'type': 'test'
        }
        
        # Create a material that partially overlaps
        test_material = {
            'x': 50,  # Centered on edge
            'y': 50,
            'width': 120,  # Extends beyond target
            'height': 80,
            'name': 'Test Material',
            'color': '#FF0000'
        }
        
        # Process intersections
        results = geom_utils.process_intersections(target_shape, [test_material])
        
        print(f"✓ Coverage: {results['coverage']:.1f}%")
        print(f"✓ Materials to keep: {len(results['materials_to_keep'])}")
        print(f"✓ Materials to remove: {len(results['materials_to_remove'])}")
        print(f"✓ Remainders: {len(results['remainders'])}")
        
        # Check that we have remainders (the falling off pieces)
        if results['remainders']:
            for i, remainder in enumerate(results['remainders']):
                print(f"  Remainder {i+1}: {remainder['width']:.1f}x{remainder['height']:.1f} cm")
        
        return len(results['remainders']) > 0
        
    except Exception as e:
        print(f"✗ Cutting logic test failed: {e}")
        return False

def test_rectangular_extraction():
    """Test rectangular piece extraction from complex remainders"""
    print("\nTesting rectangular piece extraction...")
    
    try:
        geom_utils = GeometryUtils()
        
        # Create a complex remainder shape (L-shaped)
        remainder_geom = Polygon([
            (0, 0), (100, 0), (100, 50), (50, 50), (50, 100), (0, 100)
        ])
        
        original_material = {
            'name': 'Test Material',
            'width': 100,
            'height': 100,
            'color': '#FF0000'
        }
        
        # Extract rectangular pieces
        pieces = geom_utils.extract_rectangular_pieces(remainder_geom, original_material)
        
        print(f"✓ Extracted {len(pieces)} rectangular pieces")
        
        for i, piece in enumerate(pieces):
            print(f"  Piece {i+1}: {piece['width']:.1f}x{piece['height']:.1f} cm")
            
        return len(pieces) > 0
        
    except Exception as e:
        print(f"✗ Rectangular extraction test failed: {e}")
        return False

def test_material_json_structure():
    """Test that the material JSON structure supports the new features"""
    print("\nTesting material JSON structure...")
    
    try:
        # Test materials with new structure
        test_materials = [
            {
                'name': 'Visual Test Material',
                'width': 120.5,
                'height': 200.3,
                'color': '#90EE90',
                'quantity': 5
            },
            {
                'name': 'Maradék - Original Material',
                'width': 45.2,
                'height': 67.8,
                'color': '#FFB6C1',
                'quantity': 1
            }
        ]
        
        # Test JSON serialization
        json_str = json.dumps(test_materials, ensure_ascii=False, indent=2)
        loaded_materials = json.loads(json_str)
        
        print("✓ JSON serialization works with decimal dimensions")
        print("✓ Remainder material naming convention supported")
        
        # Check that all fields are preserved
        for original, loaded in zip(test_materials, loaded_materials):
            if (original['name'] == loaded['name'] and
                abs(original['width'] - loaded['width']) < 0.01 and
                abs(original['height'] - loaded['height']) < 0.01):
                print(f"✓ Material '{original['name']}' preserved correctly")
            else:
                print(f"✗ Material '{original['name']}' not preserved correctly")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ JSON structure test failed: {e}")
        return False

def main():
    """Run all new feature tests"""
    print("New Features Test Suite")
    print("=" * 50)
    
    tests = [
        ("Visual-Only Display", test_visual_only_display),
        ("Improved Cutting Logic", test_improved_cutting_logic),
        ("Rectangular Extraction", test_rectangular_extraction),
        ("Material JSON Structure", test_material_json_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED")
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All new features work correctly!")
        print("\nNew features summary:")
        print("- Materials display as visual shapes only (no text lists)")
        print("- Scaling follows canvas zoom factor")
        print("- Cutting logic only keeps 'falling off' pieces")
        print("- Rectangular extraction from complex remainders")
    else:
        print("⚠️  Some new features need attention.")
        
    return passed == total

if __name__ == "__main__":
    main()
