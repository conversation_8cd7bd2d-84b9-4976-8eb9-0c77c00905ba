#!/usr/bin/env python3
"""
Debug script to test material adding in isolation
"""

import tkinter as tk
from tkinter import ttk
from material_manager import MaterialDialog
import sys

def test_material_dialog_gui():
    """Test the material dialog with actual GUI"""
    
    def on_add_clicked():
        print("Add button clicked!")
        dialog = MaterialDialog(root, "Test Material Dialog")
        root.wait_window(dialog.dialog)
        
        if dialog.result:
            print("SUCCESS! Material created:")
            print(f"  Name: {dialog.result['name']}")
            print(f"  Width: {dialog.result['width']}")
            print(f"  Height: {dialog.result['height']}")
            print(f"  Quantity: {dialog.result['quantity']}")
            print(f"  Color: {dialog.result['color']}")
            
            # Update result label
            result_text = f"Added: {dialog.result['name']} ({dialog.result['quantity']} pieces)"
            result_label.config(text=result_text)
        else:
            print("No result from dialog")
            result_label.config(text="No material added")
    
    # Create main window
    root = tk.Tk()
    root.title("Material Dialog Test")
    root.geometry("400x200")
    
    # Create UI
    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    ttk.Label(main_frame, text="Test Material Adding Dialog", 
              font=('Arial', 14, 'bold')).pack(pady=10)
    
    ttk.Button(main_frame, text="Add Material", 
               command=on_add_clicked).pack(pady=10)
    
    result_label = ttk.Label(main_frame, text="No material added yet", 
                            foreground='gray')
    result_label.pack(pady=10)
    
    ttk.Button(main_frame, text="Exit", 
               command=root.quit).pack(pady=10)
    
    print("Starting material dialog test...")
    print("Click 'Add Material' to test the dialog")
    
    root.mainloop()

if __name__ == "__main__":
    test_material_dialog_gui()
