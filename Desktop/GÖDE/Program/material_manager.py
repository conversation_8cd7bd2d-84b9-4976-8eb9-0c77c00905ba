"""
Material management and display panel
"""

import tkinter as tk
from tkinter import ttk, simpledialog, messagebox, colorchooser
import json
import os


class MaterialManager:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        
        # Material data
        self.material_types = []
        self.materials_enabled = False
        
        # UI elements
        self.materials_frame = None
        self.canvas = None
        self.scrollbar = None
        
        self.setup_ui()
        self.load_materials()
        
    def setup_ui(self):
        """Setup materials panel UI"""
        # Control buttons frame
        controls_frame = ttk.Frame(self.parent)
        controls_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Add material button
        ttk.Button(controls_frame, text="Anyag hozzáadása", 
                  command=self.add_material_dialog).pack(side=tk.LEFT, padx=(0, 5))
        
        # Manage materials button
        ttk.Button(controls_frame, text="Kezelés", 
                  command=self.manage_materials_dialog).pack(side=tk.LEFT)
        
        # Materials display area with scrollbar
        display_frame = ttk.Frame(self.parent)
        display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        
        # Create canvas for scrollable content
        self.canvas = tk.Canvas(display_frame, bg='lightgray')
        self.scrollbar = ttk.Scrollbar(display_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # Pack scrollbar and canvas
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Create frame inside canvas for materials
        self.materials_frame = ttk.Frame(self.canvas)
        self.canvas_window = self.canvas.create_window((0, 0), window=self.materials_frame, anchor=tk.NW)
        
        # Bind events
        self.materials_frame.bind('<Configure>', self.on_frame_configure)
        self.canvas.bind('<Configure>', self.on_canvas_configure)
        self.canvas.bind('<MouseWheel>', self.on_mousewheel)
        
        # Status label
        self.status_label = ttk.Label(self.parent, text="Válasszon ki egy falformát az anyagok aktiválásához")
        self.status_label.pack(side=tk.BOTTOM, anchor=tk.W, padx=5)
        
    def on_frame_configure(self, event):
        """Update scroll region when frame size changes"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
    def on_canvas_configure(self, event):
        """Update canvas window width when canvas size changes"""
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_window, width=canvas_width)
        
    def on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        
    def add_material_dialog(self):
        """Show dialog to add new material type"""
        print("Anyag hozzáadása dialógus megnyitása...")
        try:
            dialog = MaterialDialog(self.parent, "Új anyag hozzáadása")
            print("Dialógus létrehozva, várakozás a bezárásra...")
            self.parent.wait_window(dialog.dialog)  # Wait for dialog to close
            print("Dialógus bezárva")

            if dialog.result:
                material_type = dialog.result
                print(f"Eredmény kapva: {material_type}")
                self.material_types.append(material_type)
                self.save_materials()
                self.refresh_display()
                print(f"Anyag hozzáadva: {material_type['name']} - {material_type['quantity']} db")
            else:
                print("Nincs eredmény a dialógusból")
        except Exception as e:
            print(f"Hiba az anyag hozzáadásakor: {e}")
            import traceback
            traceback.print_exc()
            
    def manage_materials_dialog(self):
        """Show dialog to manage existing materials"""
        dialog = MaterialManagementDialog(self.parent, self.material_types)
        self.parent.wait_window(dialog.dialog)  # Wait for dialog to close

        if dialog.modified:
            self.material_types = dialog.material_types
            self.save_materials()
            self.refresh_display()
            print("Anyagok frissítve")
            
    def enable_materials(self):
        """Enable materials for drag & drop"""
        self.materials_enabled = True
        self.status_label.config(text="Húzza be az anyagokat a canvas-ra")
        self.refresh_display()
        
    def disable_materials(self):
        """Disable materials"""
        self.materials_enabled = False
        self.status_label.config(text="Válasszon ki egy falformát az anyagok aktiválásához")
        self.refresh_display()
        
    def refresh_display(self):
        """Refresh materials display - show each individual piece"""
        # Clear existing widgets
        for widget in self.materials_frame.winfo_children():
            widget.destroy()

        if not self.material_types:
            ttk.Label(self.materials_frame, text="Nincs anyag definiálva\nKattintson az 'Anyag hozzáadása' gombra").pack(pady=20)
            return

        # Display each individual material piece as visual shapes only
        piece_count = 0
        for material_type in self.material_types:
            for piece_index in range(material_type['quantity']):
                self.create_individual_piece_widget(material_type, piece_index, piece_count)
                piece_count += 1

        # Update scroll region
        self.materials_frame.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def update_display_scale(self):
        """Update display scale based on canvas zoom"""
        # Refresh display to update scaling
        self.refresh_display()
        
    def create_individual_piece_widget(self, material_type, piece_index, global_index):
        """Create widget for an individual material piece - only visual shape"""
        # Get canvas zoom factor for consistent scaling - EXACT same scale as canvas
        canvas_zoom = getattr(self.main_app.canvas_viewer, 'zoom_factor', 1.0)

        # Use the EXACT same scaling as the canvas viewer
        display_width = max(int(material_type['width'] * canvas_zoom), 8)  # Minimum 8px
        display_height = max(int(material_type['height'] * canvas_zoom), 6)  # Minimum 6px

        # Create canvas for visual representation only - NO BORDER, NO BACKGROUND
        visual_canvas = tk.Canvas(self.materials_frame,
                                 width=display_width,
                                 height=display_height,
                                 bg='white',
                                 highlightthickness=0,  # Remove border
                                 relief=tk.FLAT,        # Flat relief
                                 borderwidth=0)         # No border
        visual_canvas.pack(padx=1, pady=1)

        # Draw the material shape - could be rectangle or complex geometry
        if material_type.get('is_complex_shape', False) and 'geometry' in material_type:
            # Draw complex shape (L-shaped, etc.)
            self.draw_complex_shape(visual_canvas, material_type, display_width, display_height, canvas_zoom)
        else:
            # Draw simple rectangle - FULL SIZE, NO PADDING
            x1, y1 = 0, 0
            x2, y2 = display_width, display_height

            visual_canvas.create_rectangle(
                x1, y1, x2, y2,
                fill=material_type['color'],
                outline='black',
                width=1
            )

        # Add size label only if there's enough space
        if display_width > 25 and display_height > 12:
            visual_canvas.create_text(
                display_width / 2, display_height / 2,
                text=f"{int(material_type['width'])}×{int(material_type['height'])}",
                font=('Arial', 6),
                fill='black'
            )

        # Store material data in the canvas for drag operations
        visual_canvas.material_data = {
            'type': material_type,
            'piece_index': piece_index,
            'global_index': global_index
        }

        # Add tooltip with material info
        def show_tooltip(event):
            tooltip_text = f"{material_type['name']}\n{material_type['width']}×{material_type['height']} cm\nDarab #{piece_index + 1}"
            # Simple tooltip - could be enhanced
            visual_canvas.create_text(event.x, event.y - 20, text=tooltip_text,
                                    font=('Arial', 8), fill='blue', tags='tooltip')

        def hide_tooltip(event):
            visual_canvas.delete('tooltip')

        visual_canvas.bind('<Enter>', show_tooltip)
        visual_canvas.bind('<Leave>', hide_tooltip)

        # Enable drag & drop only if materials are enabled and shape is selected
        if self.materials_enabled:
            visual_canvas.bind('<Button-1>', lambda e, canvas=visual_canvas: self.start_drag(e, canvas))
            visual_canvas.bind('<B1-Motion>', self.on_drag)
            visual_canvas.bind('<ButtonRelease-1>', self.end_drag)
            visual_canvas.config(cursor='hand2')
        else:
            visual_canvas.config(cursor='arrow')

        return visual_canvas

    def draw_complex_shape(self, canvas, material_type, display_width, display_height, canvas_zoom):
        """Draw complex shaped material (L-shaped, etc.)"""
        try:
            geometry = material_type['geometry']

            # Get the bounds of the geometry
            bounds = geometry.bounds
            geom_width = bounds[2] - bounds[0]
            geom_height = bounds[3] - bounds[1]

            # Calculate scale to fit in display area
            scale_x = display_width / geom_width if geom_width > 0 else 1
            scale_y = display_height / geom_height if geom_height > 0 else 1

            # Get exterior coordinates
            if hasattr(geometry, 'exterior'):
                coords = list(geometry.exterior.coords)

                # Convert coordinates to canvas coordinates
                canvas_coords = []
                for x, y in coords:
                    # Normalize to 0-based coordinates
                    norm_x = (x - bounds[0]) * scale_x
                    norm_y = (y - bounds[1]) * scale_y
                    # Flip Y coordinate (canvas Y is inverted)
                    canvas_y = display_height - norm_y
                    canvas_coords.extend([norm_x, canvas_y])

                # Draw the polygon
                if len(canvas_coords) >= 6:  # At least 3 points
                    canvas.create_polygon(
                        canvas_coords,
                        fill=material_type['color'],
                        outline='black',
                        width=1
                    )
            else:
                # Fallback to rectangle if geometry is not a polygon
                canvas.create_rectangle(
                    0, 0, display_width, display_height,
                    fill=material_type['color'],
                    outline='black',
                    width=1
                )

        except Exception as e:
            print(f"Error drawing complex shape: {e}")
            # Fallback to rectangle
            canvas.create_rectangle(
                0, 0, display_width, display_height,
                fill=material_type['color'],
                outline='black',
                width=1
            )
                      
    def start_drag(self, event, canvas_widget):
        """Start dragging a material piece"""
        if not self.materials_enabled:
            return

        material_data = canvas_widget.material_data
        self.dragging_material = material_data
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root

        # Visual feedback - change cursor
        canvas_widget.config(cursor='fleur')

    def on_drag(self, event):
        """Handle material dragging"""
        if hasattr(self, 'dragging_material'):
            # Could add visual feedback here (like a ghost image)
            pass

    def end_drag(self, event):
        """End material dragging"""
        if not hasattr(self, 'dragging_material'):
            return

        material_data = self.dragging_material
        material_type = material_data['type']

        # Check if dropped on canvas
        canvas_widget = self.main_app.canvas_viewer.canvas

        # Get canvas position
        canvas_x = canvas_widget.winfo_rootx()
        canvas_y = canvas_widget.winfo_rooty()
        canvas_width = canvas_widget.winfo_width()
        canvas_height = canvas_widget.winfo_height()

        drop_x = event.x_root
        drop_y = event.y_root

        # Check if dropped within canvas bounds
        if (canvas_x <= drop_x <= canvas_x + canvas_width and
            canvas_y <= drop_y <= canvas_y + canvas_height):

            # Convert to canvas coordinates
            canvas_local_x = drop_x - canvas_x
            canvas_local_y = drop_y - canvas_y

            # Add material to canvas - this now handles complex shapes
            success = self.main_app.canvas_viewer.add_material_to_canvas(
                material_type, canvas_local_x, canvas_local_y
            )

            if success:
                # Decrease quantity
                material_type['quantity'] -= 1
                self.save_materials()
                self.refresh_display()

        # Reset cursor safely
        try:
            event.widget.config(cursor='hand2' if self.materials_enabled else 'arrow')
        except:
            pass  # Widget might be destroyed
        delattr(self, 'dragging_material')
        

        
    def update_after_processing(self, results):
        """Update materials after processing"""
        # Add remainder pieces as new materials
        for remainder in results.get('remainders', []):
            # Check if we already have this material type
            existing_material = None
            for material_type in self.material_types:
                if (material_type['name'] == remainder['name'] and
                    material_type['width'] == remainder['width'] and
                    material_type['height'] == remainder['height'] and
                    material_type['color'] == remainder['color']):
                    existing_material = material_type
                    break

            if existing_material:
                # Add to existing material quantity
                existing_material['quantity'] += remainder.get('quantity', 1)
            else:
                # Create new material type for remainder
                remainder_material = {
                    'name': remainder['name'],
                    'width': remainder['width'],
                    'height': remainder['height'],
                    'color': remainder['color'],
                    'quantity': remainder.get('quantity', 1)
                }
                self.material_types.append(remainder_material)

        self.save_materials()
        self.refresh_display()
        
    def save_materials(self):
        """Save materials to file"""
        try:
            with open('materials.json', 'w', encoding='utf-8') as f:
                json.dump(self.material_types, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving materials: {e}")
            
    def load_materials(self):
        """Load materials from file"""
        try:
            if os.path.exists('materials.json'):
                with open('materials.json', 'r', encoding='utf-8') as f:
                    self.material_types = json.load(f)
            else:
                # No default materials - user must add them
                self.material_types = []
                self.save_materials()
        except Exception as e:
            print(f"Error loading materials: {e}")
            self.material_types = []

        self.refresh_display()


class MaterialDialog:
    """Dialog for adding/editing materials"""

    def __init__(self, parent, title, material=None):
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x350")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        # Make sure dialog is visible
        self.dialog.lift()
        self.dialog.focus_force()

        # Initialize with existing material data if editing
        if material:
            self.name_var = tk.StringVar(value=material['name'])
            self.width_var = tk.StringVar(value=str(material['width']))
            self.height_var = tk.StringVar(value=str(material['height']))
            self.quantity_var = tk.StringVar(value=str(material['quantity']))
            self.color_var = tk.StringVar(value=material['color'])
        else:
            self.name_var = tk.StringVar()
            self.width_var = tk.StringVar()
            self.height_var = tk.StringVar()
            self.quantity_var = tk.StringVar()
            self.color_var = tk.StringVar(value='#F5F5F5')

        self.setup_ui()

    def setup_ui(self):
        """Setup dialog UI"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Name
        ttk.Label(main_frame, text="Név:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.name_var, width=30).grid(row=0, column=1, columnspan=2, sticky=tk.EW, pady=5)

        # Width
        ttk.Label(main_frame, text="Szélesség:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.width_var, width=15).grid(row=1, column=1, sticky=tk.EW, pady=5)
        ttk.Label(main_frame, text="cm").grid(row=1, column=2, sticky=tk.W, pady=5)

        # Height
        ttk.Label(main_frame, text="Magasság:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.height_var, width=15).grid(row=2, column=1, sticky=tk.EW, pady=5)
        ttk.Label(main_frame, text="cm").grid(row=2, column=2, sticky=tk.W, pady=5)

        # Quantity
        ttk.Label(main_frame, text="Mennyiség:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.quantity_var, width=15).grid(row=3, column=1, sticky=tk.EW, pady=5)
        ttk.Label(main_frame, text="db").grid(row=3, column=2, sticky=tk.W, pady=5)

        # Color
        ttk.Label(main_frame, text="Szín:").grid(row=4, column=0, sticky=tk.W, pady=5)
        color_frame = ttk.Frame(main_frame)
        color_frame.grid(row=4, column=1, columnspan=2, sticky=tk.EW, pady=5)

        self.color_button = tk.Button(color_frame, text="Szín választása",
                                     command=self.choose_color, width=15)
        self.color_button.pack(side=tk.LEFT)

        self.color_preview = tk.Label(color_frame, width=5, height=2, bg=self.color_var.get())
        self.color_preview.pack(side=tk.LEFT, padx=(10, 0))

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=3, pady=20)

        ttk.Button(button_frame, text="OK", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Mégse", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)

        # Configure grid weights
        main_frame.columnconfigure(1, weight=1)

    def choose_color(self):
        """Open color chooser"""
        color = colorchooser.askcolor(initialcolor=self.color_var.get())[1]
        if color:
            self.color_var.set(color)
            self.color_preview.config(bg=color)

    def ok_clicked(self):
        """Handle OK button"""
        print("OK button clicked")
        try:
            name = self.name_var.get().strip()
            width_str = self.width_var.get().strip()
            height_str = self.height_var.get().strip()
            quantity_str = self.quantity_var.get().strip()
            color = self.color_var.get()

            print(f"Input values: name='{name}', width='{width_str}', height='{height_str}', quantity='{quantity_str}', color='{color}'")

            if not name:
                print("Error: Name is empty")
                messagebox.showerror("Hiba", "A név megadása kötelező!")
                return

            if not width_str or not height_str or not quantity_str:
                print("Error: Some numeric fields are empty")
                messagebox.showerror("Hiba", "Minden mező kitöltése kötelező!")
                return

            width = float(width_str)
            height = float(height_str)
            quantity = int(quantity_str)

            print(f"Parsed values: width={width}, height={height}, quantity={quantity}")

            if width <= 0 or height <= 0:
                print("Error: Invalid dimensions")
                messagebox.showerror("Hiba", "A méretek pozitív számok legyenek!")
                return

            if quantity < 0:
                print("Error: Invalid quantity")
                messagebox.showerror("Hiba", "A mennyiség nem lehet negatív!")
                return

            self.result = {
                'name': name,
                'width': width,
                'height': height,
                'quantity': quantity,
                'color': color
            }

            print(f"Result created: {self.result}")
            self.dialog.destroy()

        except ValueError as e:
            print(f"ValueError: {e}")
            messagebox.showerror("Hiba", "Hibás számformátum!")

    def cancel_clicked(self):
        """Handle Cancel button"""
        self.dialog.destroy()


class MaterialManagementDialog:
    """Dialog for managing existing materials"""

    def __init__(self, parent, material_types):
        self.material_types = material_types.copy()
        self.modified = False

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Anyagok kezelése")
        self.dialog.geometry("600x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.setup_ui()
        self.refresh_list()

    def setup_ui(self):
        """Setup dialog UI"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # List frame
        list_frame = ttk.LabelFrame(main_frame, text="Anyagok")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Treeview for materials
        columns = ('name', 'width', 'height', 'quantity')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

        # Define headings
        self.tree.heading('name', text='Név')
        self.tree.heading('width', text='Szélesség')
        self.tree.heading('height', text='Magasság')
        self.tree.heading('quantity', text='Mennyiség')

        # Configure column widths
        self.tree.column('name', width=200)
        self.tree.column('width', width=100)
        self.tree.column('height', width=100)
        self.tree.column('quantity', width=100)

        # Scrollbar for treeview
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # Pack treeview and scrollbar
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="Szerkesztés", command=self.edit_material).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Törlés", command=self.delete_material).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Bezárás", command=self.close_dialog).pack(side=tk.RIGHT, padx=5)

    def refresh_list(self):
        """Refresh materials list"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Add materials
        for i, material in enumerate(self.material_types):
            self.tree.insert('', 'end', iid=i, values=(
                material['name'],
                f"{material['width']} cm",
                f"{material['height']} cm",
                f"{material['quantity']} db"
            ))

    def edit_material(self):
        """Edit selected material"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("Figyelem", "Válasszon ki egy anyagot!")
            return

        index = int(selection[0])
        material = self.material_types[index]

        dialog = MaterialDialog(self.dialog, "Anyag szerkesztése", material)
        self.dialog.wait_window(dialog.dialog)

        if dialog.result:
            self.material_types[index] = dialog.result
            self.modified = True
            self.refresh_list()

    def delete_material(self):
        """Delete selected material"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("Figyelem", "Válasszon ki egy anyagot!")
            return

        index = int(selection[0])
        material = self.material_types[index]

        if messagebox.askyesno("Megerősítés", f"Biztosan törli a '{material['name']}' anyagot?"):
            del self.material_types[index]
            self.modified = True
            self.refresh_list()

    def close_dialog(self):
        """Close dialog"""
        self.dialog.destroy()
