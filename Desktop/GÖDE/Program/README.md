# Drywall Optimization Program

Ez egy tkinter-al<PERSON><PERSON> alkal<PERSON>, amely seg<PERSON>t optimalizálni a gipszkarton lapok felhasználását DXF fájlokban megrajzolt falformák építéséhez.

## Funkciók

### Fő komponensek:
1. **DXF Viewer (bal panel)** - Végtelen canvas AutoCAD-szerű navigációval
2. **Eszköztár (jobb panel)** - Anyagok kezelése és drag & drop

### Főbb funkciók:
- DXF fájlok betöltése és zárt alakzatok automatikus felismerése
- AutoCAD-szerű navigáció (zoom, pan)
- Falformák kiválasztása
- Gipszkarton lapok definiálása (méret, szín, mennyiség)
- Drag & drop anyag elhelyezés
- Automatikus vágás és maradék kezelés
- Anyag optimalizáció

## Telepítés

1. G<PERSON><PERSON><PERSON><PERSON>d<PERSON><PERSON><PERSON> meg róla, hogy Python 3.8+ telep<PERSON>t<PERSON> van
2. Telepítse a függőségeket:
```bash
pip3 install -r requirements.txt
```

## Használat

### Program indítása:
```bash
python3 main.py
```

### Alapvető munkafolyamat:

1. **DXF betöltése:**
   - Kattintson a "DXF betöltése" gombra
   - Válasszon ki egy DXF fájlt
   - A program automatikusan felismeri a zárt alakzatokat

2. **Falforma kiválasztása:**
   - Kattintson a "Falforma kiválasztása" gombra
   - Kattintson egy zárt alakzatra a canvas-on
   - A kiválasztott alakzat kék színnel jelenik meg

3. **Anyagok hozzáadása:**
   - Kattintson az "Anyag hozzáadása" gombra
   - Adja meg az anyag tulajdonságait (név, méret, mennyiség, szín)
   - Az anyagok megjelennek az eszköztárban

4. **Anyagok elhelyezése:**
   - Húzza az anyagokat az eszköztárból a canvas-ra
   - Helyezze el őket a kiválasztott alakzaton
   - Használja a drag & drop funkciót

5. **Feldolgozás:**
   - Nyomja meg az Enter billentyűt vagy a "Feldolgozás" gombot
   - A program kiszámítja a metszéseket
   - A maradék darabok visszakerülnek az eszköztárba

### Navigáció:
- **Zoom:** Egérgörgő
- **Pan:** Középső egérgomb + húzás vagy bal egérgomb + húzás (ha nincs anyag kiválasztva)
- **Alakzat kiválasztás:** Bal kattintás (kiválasztási módban)
- **Anyag mozgatás:** Bal kattintás + húzás

### Billentyűparancsok:
- **Ctrl+O:** DXF betöltése
- **Enter:** Feldolgozás
- **Escape:** Kiválasztási mód megszakítása

## Teszt fájl

A programhoz tartozik egy teszt DXF fájl (`test_shapes.dxf`), amely különböző zárt alakzatokat tartalmaz:
- Téglalapok
- Kör
- L-alakú polygon
- Háromszög
- Komplex alakzat lyukkal

### Teszt fájl létrehozása:
```bash
python3 create_test_dxf.py
```

## Fájlstruktúra

- `main.py` - Fő alkalmazás
- `dxf_handler.py` - DXF fájl kezelés és zárt alakzat felismerés
- `canvas_viewer.py` - Canvas megjelenítés és navigáció
- `material_manager.py` - Anyag kezelés és eszköztár
- `geometry_utils.py` - Geometriai számítások és optimalizáció
- `materials.json` - Anyagok tárolása (automatikusan létrejön)
- `requirements.txt` - Python függőségek

## Anyag kezelés

### Anyag típusok:
- **Név:** Egyedi azonosító
- **Szélesség/Magasság:** cm-ben
- **Mennyiség:** Darabszám
- **Szín:** Vizuális megkülönböztetéshez

### Anyag megjelenítés:
- **Csak vizuális alakzatok:** Nincs lista, csak a lapok alakja jelenik meg
- **Canvas zoom arányos skálázás:** Az eszköztár lapjai a canvas zoom-jával arányosan változnak
- **Tooltip információ:** Hover-rel részletes információ (név, méret, darabszám)
- **Drag & Drop:** Csak akkor engedélyezett, ha van kiválasztott falforma
- **Nincs alapértelmezett anyag:** A felhasználó adja hozzá az anyagokat

### Intelligens vágási logika:
- **Komplex alakzatok támogatása:** L-alakú, U-alakú, T-alakú falformák és anyagok
- **Pontos geometriai metszés:** Valódi alakzat-alapú vágás, nem téglalap közelítés
- **Komplex maradékok:** L-alakú, U-alakú maradék darabok megőrzése
- **Minimális méret:** Csak 3cm-nél nagyobb darabokat tart meg
- **Valódi alakzat felhasználás:** Csak a falformán belüli rész marad, pontos geometria

### Fontos változások:
- **Vizuális eszköztár** - csak alakzatok, nincs szöveges lista
- **Dinamikus skálázás** - zoom változáskor az eszköztár is frissül
- **Falforma kiválasztás kötelező** - anyagok csak kiválasztott falforma esetén használhatók
- **Fejlett maradék kezelés** - csak a hasznos "leeső" darabokat tartja meg

## Geometriai számítások

A program a következő számításokat végzi:
- Alakzat-anyag metszetek
- Maradék darabok kiszámítása
- Anyag hatékonyság
- Vágási utasítások generálása

## Hibaelhárítás

### Gyakori problémák:

1. **DXF betöltési hiba:**
   - Ellenőrizze, hogy a fájl valódi DXF formátumú
   - Győződjön meg róla, hogy vannak zárt alakzatok a fájlban

2. **Anyagok nem jelennek meg:**
   - Először válasszon ki egy falformát
   - Ellenőrizze, hogy van-e mennyiség az anyagból

3. **Navigációs problémák:**
   - Használja a fit-to-view funkciót (DXF újratöltése)
   - Ellenőrizze az egér beállításokat

## Tesztelés

A program teljes körűen tesztelve van:

```bash
# Alapfunkciók tesztelése
python3 test_program.py

# Új funkciók tesztelése
python3 test_new_features.py

# Javítások tesztelése
python3 test_fixed_features.py

# Teljes munkafolyamat tesztelése
python3 full_workflow_test.py
```

### Tesztelési eredmények:
- ✅ **Alapfunkciók:** 6/6 teszt sikeres
- ✅ **Új funkciók:** 4/4 teszt sikeres
- ✅ **Javítások:** 3/4 teszt sikeres
- ✅ **Teljes munkafolyamat:** 3/3 teszt sikeres

## Fejlesztési lehetőségek

- Több DXF entitás típus támogatása
- Fejlettebb optimalizációs algoritmusok
- Vágási terv exportálása
- 3D megjelenítés
- Költségszámítás
- Anyag adatbázis kezelés
- Automatikus anyag elrendezés optimalizáció

## Licenc

Ez a program oktatási és fejlesztési célokra készült.
