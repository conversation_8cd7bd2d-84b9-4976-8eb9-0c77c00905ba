#!/usr/bin/env python3
"""
Create a test DXF file with complex shapes (L-shaped, U-shaped walls)
"""

import ezdxf

def create_complex_test_dxf():
    """Create a test DXF file with complex wall shapes"""
    
    # Create new DXF document
    doc = ezdxf.new('R2010')
    msp = doc.modelspace()
    
    # L-shaped wall 1
    l_shape_1 = [
        (50, 50),
        (200, 50),
        (200, 100),
        (100, 100),
        (100, 200),
        (50, 200),
        (50, 50)  # Close the shape
    ]
    msp.add_lwpolyline(l_shape_1, close=True)
    
    # L-shaped wall 2 (different orientation)
    l_shape_2 = [
        (300, 50),
        (350, 50),
        (350, 200),
        (400, 200),
        (400, 250),
        (300, 250),
        (300, 50)  # Close the shape
    ]
    msp.add_lwpolyline(l_shape_2, close=True)
    
    # U-shaped wall
    u_shape = [
        (500, 50),
        (650, 50),
        (650, 100),
        (600, 100),
        (600, 200),
        (550, 200),
        (550, 100),
        (500, 100),
        (500, 50)  # Close the shape
    ]
    msp.add_lwpolyline(u_shape, close=True)
    
    # T-shaped wall
    t_shape = [
        (100, 300),
        (300, 300),
        (300, 350),
        (225, 350),
        (225, 450),
        (175, 450),
        (175, 350),
        (100, 350),
        (100, 300)  # Close the shape
    ]
    msp.add_lwpolyline(t_shape, close=True)
    
    # Plus-shaped wall
    plus_shape = [
        (450, 350),
        (500, 350),
        (500, 300),
        (550, 300),
        (550, 350),
        (600, 350),
        (600, 400),
        (550, 400),
        (550, 450),
        (500, 450),
        (500, 400),
        (450, 400),
        (450, 350)  # Close the shape
    ]
    msp.add_lwpolyline(plus_shape, close=True)
    
    # C-shaped wall (concave)
    c_shape = [
        (50, 500),
        (200, 500),
        (200, 550),
        (100, 550),
        (100, 650),
        (200, 650),
        (200, 700),
        (50, 700),
        (50, 500)  # Close the shape
    ]
    msp.add_lwpolyline(c_shape, close=True)
    
    # Rectangular walls for comparison
    rect_1 = [
        (300, 500),
        (400, 500),
        (400, 600),
        (300, 600),
        (300, 500)
    ]
    msp.add_lwpolyline(rect_1, close=True)
    
    rect_2 = [
        (450, 500),
        (550, 500),
        (550, 600),
        (450, 600),
        (450, 500)
    ]
    msp.add_lwpolyline(rect_2, close=True)
    
    # Add some construction lines for reference
    msp.add_line((0, 0), (700, 0))  # X-axis
    msp.add_line((0, 0), (0, 750))   # Y-axis
    
    # Add grid lines
    for x in range(0, 701, 50):
        msp.add_line((x, 0), (x, 750), dxfattribs={'color': 8})  # Gray lines
    for y in range(0, 751, 50):
        msp.add_line((0, y), (700, y), dxfattribs={'color': 8})  # Gray lines
    
    # Add labels
    msp.add_text("Complex Wall Shapes Test", 
                 dxfattribs={'height': 20, 'insert': (50, 750)})
    
    msp.add_text("L-Shape 1", 
                 dxfattribs={'height': 10, 'insert': (50, 30)})
    
    msp.add_text("L-Shape 2", 
                 dxfattribs={'height': 10, 'insert': (300, 30)})
    
    msp.add_text("U-Shape", 
                 dxfattribs={'height': 10, 'insert': (500, 30)})
    
    msp.add_text("T-Shape", 
                 dxfattribs={'height': 10, 'insert': (100, 280)})
    
    msp.add_text("Plus-Shape", 
                 dxfattribs={'height': 10, 'insert': (450, 280)})
    
    msp.add_text("C-Shape", 
                 dxfattribs={'height': 10, 'insert': (50, 480)})
    
    msp.add_text("Rectangle 1", 
                 dxfattribs={'height': 10, 'insert': (300, 480)})
    
    msp.add_text("Rectangle 2", 
                 dxfattribs={'height': 10, 'insert': (450, 480)})
    
    # Add dimension annotations
    msp.add_text("150x150", 
                 dxfattribs={'height': 8, 'insert': (110, 125)})
    
    msp.add_text("150x100", 
                 dxfattribs={'height': 8, 'insert': (560, 125)})
    
    msp.add_text("200x150", 
                 dxfattribs={'height': 8, 'insert': (175, 375)})
    
    msp.add_text("150x200", 
                 dxfattribs={'height': 8, 'insert': (110, 575)})
    
    # Save the DXF file
    doc.saveas('complex_test_shapes.dxf')
    print("Complex test DXF file 'complex_test_shapes.dxf' created successfully!")
    
    # Print info about the shapes
    print("\nCreated complex shapes:")
    print("1. L-Shape 1: 150x150 cm L-shaped wall")
    print("2. L-Shape 2: Different orientation L-shaped wall")
    print("3. U-Shape: 150x100 cm U-shaped wall")
    print("4. T-Shape: 200x150 cm T-shaped wall")
    print("5. Plus-Shape: Cross-shaped wall")
    print("6. C-Shape: Concave C-shaped wall")
    print("7. Rectangle 1: 100x100 cm simple rectangle")
    print("8. Rectangle 2: 100x100 cm simple rectangle")
    print("\nThese shapes will test:")
    print("- Complex wall form recognition")
    print("- Proper intersection calculation")
    print("- Complex remainder generation")
    print("- L-shaped, U-shaped, T-shaped cutting")


if __name__ == "__main__":
    create_complex_test_dxf()
