#!/usr/bin/env python3
"""
Create a test DXF file with some closed shapes for testing
"""

import ezdxf
import math


def create_test_dxf():
    """Create a test DXF file with various closed shapes"""
    
    # Create new DXF document
    doc = ezdxf.new('R2010')
    msp = doc.modelspace()
    
    # Rectangle 1 - Simple rectangle
    rect1_points = [
        (100, 100),
        (300, 100),
        (300, 200),
        (100, 200),
        (100, 100)  # Close the rectangle
    ]
    msp.add_lwpolyline(rect1_points, close=True)
    
    # Rectangle 2 - Another rectangle
    rect2_points = [
        (400, 150),
        (600, 150),
        (600, 300),
        (400, 300),
        (400, 150)  # Close the rectangle
    ]
    msp.add_lwpolyline(rect2_points, close=True)
    
    # Circle
    msp.add_circle((200, 400), radius=50)
    
    # L-shaped polygon
    l_shape_points = [
        (50, 50),
        (150, 50),
        (150, 100),
        (100, 100),
        (100, 150),
        (50, 150),
        (50, 50)  # Close the shape
    ]
    msp.add_lwpolyline(l_shape_points, close=True)
    
    # Triangle
    triangle_points = [
        (500, 50),
        (600, 50),
        (550, 120),
        (500, 50)  # Close the triangle
    ]
    msp.add_lwpolyline(triangle_points, close=True)
    
    # Complex shape with hole (using separate polylines)
    outer_shape = [
        (700, 100),
        (900, 100),
        (900, 300),
        (700, 300),
        (700, 100)
    ]
    msp.add_lwpolyline(outer_shape, close=True)
    
    # Inner hole
    inner_hole = [
        (750, 150),
        (850, 150),
        (850, 250),
        (750, 250),
        (750, 150)
    ]
    msp.add_lwpolyline(inner_hole, close=True)
    
    # Add some construction lines (not closed shapes)
    msp.add_line((0, 0), (1000, 0))  # X-axis
    msp.add_line((0, 0), (0, 500))   # Y-axis
    
    # Add some dimensions/text for reference
    msp.add_text("Test DXF for Drywall Optimization",
                 dxfattribs={'height': 20, 'insert': (50, 450)})

    msp.add_text("Rectangle 1: 200x100",
                 dxfattribs={'height': 10, 'insert': (100, 80)})

    msp.add_text("Rectangle 2: 200x150",
                 dxfattribs={'height': 10, 'insert': (400, 130)})

    msp.add_text("Circle: R=50",
                 dxfattribs={'height': 10, 'insert': (175, 350)})

    msp.add_text("L-Shape",
                 dxfattribs={'height': 10, 'insert': (50, 30)})

    msp.add_text("Triangle",
                 dxfattribs={'height': 10, 'insert': (500, 30)})

    msp.add_text("Complex with hole",
                 dxfattribs={'height': 10, 'insert': (700, 80)})
    
    # Save the DXF file
    doc.saveas('test_shapes.dxf')
    print("Test DXF file 'test_shapes.dxf' created successfully!")
    
    # Print some info about the shapes
    print("\nCreated shapes:")
    print("1. Rectangle 1: 200x100 cm at (100,100)")
    print("2. Rectangle 2: 200x150 cm at (400,150)")
    print("3. Circle: radius 50 cm at (200,400)")
    print("4. L-Shape: complex polygon")
    print("5. Triangle: simple triangle")
    print("6. Complex shape with hole")
    print("\nYou can now load this file in the drywall optimization program!")


if __name__ == "__main__":
    create_test_dxf()
