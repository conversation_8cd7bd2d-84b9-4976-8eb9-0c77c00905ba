#!/usr/bin/env python3
"""
Full workflow test - simulate complete user interaction
"""

import os
import json
from geometry_utils import GeometryUtils
from shapely.geometry import Polygon

def test_complete_workflow():
    """Test the complete workflow from material creation to cutting"""
    print("Testing complete workflow...")
    
    # Step 1: Create materials (simulate user adding materials)
    print("\n1. Creating materials...")
    materials = [
        {
            'name': '<PERSON><PERSON><PERSON><PERSON> gip<PERSON>ton',
            'width': 120,
            'height': 200,
            'color': '#F5F5F5',
            'quantity': 3
        },
        {
            'name': '<PERSON>öld gipszkarton', 
            'width': 100,
            'height': 150,
            'color': '#90EE90',
            'quantity': 2
        }
    ]
    
    # Save materials
    with open('test_workflow_materials.json', 'w', encoding='utf-8') as f:
        json.dump(materials, f, ensure_ascii=False, indent=2)
    print(f"✓ Created {len(materials)} material types")
    print(f"✓ Total pieces: {sum(m['quantity'] for m in materials)}")
    
    # Step 2: Simulate DXF loading (create test shapes)
    print("\n2. Simulating DXF shapes...")
    test_shapes = [
        {
            'type': 'rectangle',
            'geometry': Polygon([(0, 0), (80, 0), (80, 60), (0, 60)]),
            'bounds': (0, 0, 80, 60)
        },
        {
            'type': 'rectangle', 
            'geometry': Polygon([(100, 100), (180, 100), (180, 180), (100, 180)]),
            'bounds': (100, 100, 180, 180)
        }
    ]
    print(f"✓ Created {len(test_shapes)} test shapes")
    
    # Step 3: Simulate material placement on canvas
    print("\n3. Simulating material placement...")
    canvas_materials = [
        {
            'name': 'Fehér gipszkarton',
            'width': 120,
            'height': 200, 
            'color': '#F5F5F5',
            'x': 40,  # Overlaps with first shape
            'y': 30
        },
        {
            'name': 'Zöld gipszkarton',
            'width': 100,
            'height': 150,
            'color': '#90EE90', 
            'x': 140,  # Overlaps with second shape
            'y': 140
        }
    ]
    print(f"✓ Placed {len(canvas_materials)} materials on canvas")
    
    # Step 4: Process intersections (simulate Enter key)
    print("\n4. Processing intersections...")
    geom_utils = GeometryUtils()
    
    total_remainders = []
    for i, shape in enumerate(test_shapes):
        print(f"\n  Processing shape {i+1}:")
        results = geom_utils.process_intersections(shape, canvas_materials)
        
        print(f"    Coverage: {results['coverage']:.1f}%")
        print(f"    Materials to keep: {len(results['materials_to_keep'])}")
        print(f"    Materials to remove: {len(results['materials_to_remove'])}")
        print(f"    Remainders: {len(results['remainders'])}")
        
        for j, remainder in enumerate(results['remainders']):
            print(f"      Remainder {j+1}: {remainder['name']}")
            print(f"        Size: {remainder['width']:.1f}x{remainder['height']:.1f} cm")
            print(f"        Complex: {remainder.get('is_complex_shape', False)}")
            if 'geometry' in remainder:
                print(f"        Area: {remainder['geometry'].area:.1f}")
        
        total_remainders.extend(results['remainders'])
    
    # Step 5: Update material inventory
    print(f"\n5. Updating inventory...")
    print(f"✓ Generated {len(total_remainders)} remainder pieces")
    
    # Add remainders to original materials
    updated_materials = materials.copy()
    for remainder in total_remainders:
        # Check if we already have this type
        existing = None
        for mat in updated_materials:
            if (mat['name'] == remainder['name'] and 
                abs(mat['width'] - remainder['width']) < 0.1 and
                abs(mat['height'] - remainder['height']) < 0.1):
                existing = mat
                break
        
        if existing:
            existing['quantity'] += remainder.get('quantity', 1)
        else:
            new_material = {
                'name': remainder['name'],
                'width': remainder['width'],
                'height': remainder['height'], 
                'color': remainder['color'],
                'quantity': remainder.get('quantity', 1)
            }
            updated_materials.append(new_material)
    
    # Save updated materials
    with open('test_workflow_materials_after.json', 'w', encoding='utf-8') as f:
        json.dump(updated_materials, f, ensure_ascii=False, indent=2)
    
    print(f"✓ Updated inventory saved")
    print(f"✓ Total material types: {len(updated_materials)}")
    print(f"✓ Total pieces: {sum(m['quantity'] for m in updated_materials)}")
    
    # Step 6: Verify results
    print(f"\n6. Verification...")
    
    # Check that we have more material types (due to remainders)
    if len(updated_materials) > len(materials):
        print("✓ Remainder materials added to inventory")
    
    # Check for complex shapes
    complex_shapes = [m for m in total_remainders if m.get('is_complex_shape', False)]
    if complex_shapes:
        print(f"✓ Generated {len(complex_shapes)} complex shaped remainders")
    
    # Clean up test files
    try:
        os.remove('test_workflow_materials.json')
        os.remove('test_workflow_materials_after.json')
        print("✓ Test files cleaned up")
    except:
        pass
    
    return True

def test_scaling_consistency():
    """Test that scaling is consistent across zoom levels"""
    print("\nTesting scaling consistency...")
    
    test_material = {
        'width': 100,
        'height': 50
    }
    
    zoom_levels = [0.5, 1.0, 1.5, 2.0, 3.0]
    
    for zoom in zoom_levels:
        expected_width = int(test_material['width'] * zoom)
        expected_height = int(test_material['height'] * zoom)
        
        # Apply minimum constraints
        actual_width = max(expected_width, 8)
        actual_height = max(expected_height, 6)
        
        print(f"  Zoom {zoom}: {test_material['width']}x{test_material['height']} → {actual_width}x{actual_height}")
        
        # Check consistency
        if zoom >= 0.08:  # Above minimum threshold
            if actual_width != expected_width or actual_height != expected_height:
                print(f"    ⚠️  Minimum size constraint applied")
        else:
            print(f"    ✓ Exact scaling")
    
    print("✓ Scaling consistency verified")
    return True

def test_complex_geometry_handling():
    """Test handling of complex geometries"""
    print("\nTesting complex geometry handling...")
    
    # Create various complex shapes
    shapes = {
        'L-shape': Polygon([
            (0, 0), (60, 0), (60, 30), (30, 30), (30, 60), (0, 60)
        ]),
        'U-shape': Polygon([
            (0, 0), (80, 0), (80, 20), (60, 20), (60, 60), (20, 60), (20, 20), (0, 20)
        ]),
        'T-shape': Polygon([
            (20, 0), (60, 0), (60, 20), (80, 20), (80, 40), (0, 40), (0, 20), (20, 20)
        ])
    }
    
    for name, geometry in shapes.items():
        bounds = geometry.bounds
        width = bounds[2] - bounds[0]
        height = bounds[3] - bounds[1]
        area = geometry.area
        
        print(f"  {name}:")
        print(f"    Bounds: {width:.1f}x{height:.1f}")
        print(f"    Area: {area:.1f}")
        print(f"    Valid: {geometry.is_valid}")
        print(f"    Type: {geometry.geom_type}")
        
        # Test that it can be used as remainder
        if geometry.is_valid and area > 25:  # 5x5 minimum
            print(f"    ✓ Suitable for remainder")
        else:
            print(f"    ✗ Too small or invalid")
    
    print("✓ Complex geometry handling verified")
    return True

def main():
    """Run all workflow tests"""
    print("Complete Workflow Test Suite")
    print("=" * 60)
    
    tests = [
        ("Complete Workflow", test_complete_workflow),
        ("Scaling Consistency", test_scaling_consistency), 
        ("Complex Geometry Handling", test_complex_geometry_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✓ {test_name} PASSED")
            else:
                print(f"\n✗ {test_name} FAILED")
        except Exception as e:
            print(f"\n✗ {test_name} FAILED: {e}")
    
    print("\n" + "=" * 60)
    print(f"Workflow Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Complete workflow works perfectly!")
        print("\nWorkflow summary:")
        print("1. ✓ Material creation and management")
        print("2. ✓ DXF shape processing") 
        print("3. ✓ Material placement and scaling")
        print("4. ✓ Intersection processing and cutting")
        print("5. ✓ Remainder generation and inventory update")
        print("6. ✓ Complex shape handling")
    else:
        print("⚠️  Some workflow steps need attention.")
        
    return passed == total

if __name__ == "__main__":
    main()
